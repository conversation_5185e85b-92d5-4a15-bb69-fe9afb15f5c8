import { <PERSON><PERSON><PERSON><PERSON>, StatusFilter, Variant } from '@/components/globals';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { Button, Label, TextInput } from 'flowbite-react';
import { FaPlus } from 'react-icons/fa';
import { HiSearch } from 'react-icons/hi';
import { useMemo, useState } from 'react';
import { AddSuportTicketModal } from './add-support-ticket-modal';
import { GroupSupportTickets } from '@/graphql/declarations/support';
import moment from 'moment';
import { QueryMode } from '@/graphql/generated/graphql';
import useDebounce from '@/components/hooks/useDebounce';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { mockSupportTickets, supportCategories } from '@/lib/mock/support-mock';
import { TicketDetailsModal } from './ticket-details-modal';

export enum SUPPORTCASE {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  RESOLVED = 'RESOLVED',
}

export const getTransactionStatusChip = (status: SUPPORTCASE): [Variant, string] => {
  const statusMap: Record<SUPPORTCASE, Variant> = {
    // [TransactionStatus.pending]: 'warning',
    // [TransactionStatus.completed]: 'success',
    // [TransactionStatus.failed]: 'danger',
    // [TransactionStatus.settled]: 'success',
    // [TransactionStatus.onHold]: 'info',
    // [TransactionStatus.refunded]: 'neutral',
    // [TransactionStatus.chargeback]: 'danger',
    [SUPPORTCASE.OPEN]: 'info',
    [SUPPORTCASE.CLOSED]: 'neutral',
    [SUPPORTCASE.RESOLVED]: 'neutral',
  };

  const variant = statusMap[status] || 'neutral';
  const label = status?.charAt(0).toUpperCase() + status?.slice(1);

  return [variant, label];
};

export const SupportTab = () => {
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [isSortAsc, setIsSortAsc] = useState(false);
  const [sortKey, setSortKey] = useState('createdAt');
  const [statusFilter, setStatusFilter] = useState('All');
  const [addCaseModal, setAddCaseModall] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [selectedTicketId, setSelectedTicketId] = useState<string | null>(null);
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const supportRows = useMemo(() => {
    // Filter tickets based on search and status
    return mockSupportTickets.filter((ticket) => {
      const matchesSearch = debouncedSearchQuery
        ? ticket.title.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          ticket.description.toLowerCase().includes(debouncedSearchQuery.toLowerCase())
        : true;

      const matchesStatus = statusFilter === 'All' ? true : ticket.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  }, [debouncedSearchQuery, statusFilter]);

  const columns: Column[] = [
    {
      key: 'id',
      header: 'Case #',
      sortable: false,
      renderCell: (row) => (
        <button
          onClick={() => setSelectedTicketId(row.id)}
          className="text-blue-600 hover:underline"
        >
          {row.id}
        </button>
      ),
    },
    {
      key: 'title',
      header: 'Title',
      sortable: true,
      onServerSort(key) {
        setSortKey(key as keyof (typeof supportRows)[0]);
        setIsSortAsc((v) => !v);
      },
    },
    {
      key: 'category',
      header: 'Category',
      sortable: true,
      valueGetter: (row) =>
        supportCategories.find((v) => v.id === row.category)?.label || 'General Inquiry',
      onServerSort(key) {
        setSortKey(key as keyof (typeof supportRows)[0]);
        setIsSortAsc((v) => !v);
      },
    },
    {
      key: 'priority',
      header: 'Priority',
      sortable: true,
      renderCell: (row) => {
        const priorityColors = {
          HIGH: 'bg-red-100 text-red-800',
          MEDIUM: 'bg-yellow-100 text-yellow-800',
          LOW: 'bg-green-100 text-green-800',
        };
        return (
          <span
            className={`rounded-full px-2 py-1 text-xs font-medium ${priorityColors[row.priority]}`}
          >
            {row.priority}
          </span>
        );
      },
      onServerSort(key) {
        setSortKey(key as keyof (typeof supportRows)[0]);
        setIsSortAsc((v) => !v);
      },
    },
    {
      key: 'createdAt',
      header: 'Creation Date',
      renderCell: (row) => {
        return <span>{moment(row.createdAt).format('DD/MM/YYYY hh:mm A')}</span>;
      },
      sortable: true,
      onServerSort(key) {
        setSortKey(key as keyof (typeof supportRows)[0]);
        setIsSortAsc((v) => !v);
      },
    },
    {
      key: 'lastMessageCreatedAt',
      header: 'Last Message Date',
      renderCell: (row) => {
        return (
          <span>
            {row.lastMessageCreatedAt
              ? moment(row.lastMessageCreatedAt).format('DD/MM/YYYY hh:mm A')
              : '- No Messages Yet -'}
          </span>
        );
      },
    },
    {
      key: 'status',
      header: 'Status',
      width: '100px',
      renderCell: (row) => {
        const statusColors = {
          OPEN: 'bg-blue-100 text-blue-800',
          IN_PROGRESS: 'bg-yellow-100 text-yellow-800',
          RESOLVED: 'bg-green-100 text-green-800',
          CLOSED: 'bg-gray-100 text-gray-800',
        };
        return (
          <span
            className={`rounded-full px-2 py-1 text-xs font-medium ${statusColors[row.status]}`}
          >
            {row.status}
          </span>
        );
      },
      sortable: true,
      onServerSort(key) {
        setSortKey(key as keyof (typeof supportRows)[0]);
        setIsSortAsc((v) => !v);
      },
    },
  ];

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: GroupSupportTickets,
      variables: {
        where: {
          OR: [
            {
              title: {
                contains: debouncedSearchQuery,
                mode: QueryMode.Insensitive,
              },
            },
            { description: { contains: debouncedSearchQuery } },
          ],
          status: statusFilter === 'All' ? undefined : { equals: statusFilter },
        },
        orderBy: [{ [sortKey]: isSortAsc ? 'asc' : 'desc' }],
        take: 50000,
        skip: 0,
      },
    });

    return result?.data?.groupSupportTickets ?? [];
  };

  return (
    <>
      <div className="items-bottom flex justify-between">
        <PageHeader text="My Cases" />
        <Button
          className="mt-[20px] h-[38px] p-0"
          color="blue"
          onClick={() => setAddCaseModall(true)}
        >
          <div className="flex items-center gap-x-3">
            <FaPlus className="text-xl" />
            <span>New Case</span>
          </div>
        </Button>
      </div>
      <div className="mt-4 hidden items-center gap-4 border-b border-gray-300 py-2 dark:divide-gray-700 sm:mb-0 sm:flex sm:justify-end sm:divide-x sm:divide-gray-100">
        {/* TODO: search not ready  */}
        <form className="hidden lg:block">
          <div className="flex">
            <Label htmlFor="search" className="sr-only">
              Search
            </Label>
            <TextInput
              className="w-full rounded-br-none rounded-tr-none"
              icon={HiSearch}
              id="search"
              name="search"
              placeholder="Search"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              type="search"
            />
            <Button color="blue" className="-ml-2 rounded-l-lg rounded-bl-none rounded-tl-none">
              Search
            </Button>
          </div>
        </form>
        <div className="flex gap-2">
          {/* //NOTE: hide for summit */}
          {/* <div className="">
            <Button className="h-[38px] p-0" color="gray">
              <div className="flex items-center gap-x-3">
                <HiFilter className="text-xl" />
                <span>Filter</span>
              </div>
            </Button>
          </div> */}

          <div className="flex items-center space-x-2 sm:space-x-3">
            {/* <ExportCSV2
              fileName="support-cases"
              fetchFunction={async () => {
                const allSupportData = await apolloClient.query({
                  query: GroupSupportTickets,
                  variables: {
                    where: {},
                    orderBy: [
                      {
                        createdAt: OrderDirection.Desc,
                      },
                    ],
                    take: 1000,
                    skip: 0,
                  },
                });

                return (
                  allSupportData.data.groupSupportTickets?.map((row) => ({
                    id: row.id,
                    title: row.title,
                    category: row.category,
                    createdAt: moment(row.createdAt).format('DD/MM/YYYY hh:mm A'),
                    lastMessageCreatedAt: moment(row.lastMessageCreatedAt).format(
                      'DD/MM/YYYY hh:mm A',
                    ),
                    status: row.status,
                  })) ?? []
                );
              }}
            /> */}
            <ExportCSV4
              columns={columns}
              reportName="support-cases"
              dataFetcher={fetchExportData}
            />
          </div>
        </div>
      </div>
      <DataGridView
        columns={columns}
        rows={supportRows ?? []}
        pageSize={pageSize}
        currentPage={currentPage}
        isLoading={false}
        actionComponent={
          <StatusFilter
            value={statusFilter}
            setValue={setStatusFilter}
            statusList={Object.values(SUPPORTCASE)}
          />
        }
        mode="server"
        onPageChange={setCurrentPage}
        onPageSizeChange={setPageSize}
        totalRecords={supportRows.length}
      />

      <AddSuportTicketModal isOpen={addCaseModal} onClose={() => setAddCaseModall(false)} />
      <TicketDetailsModal
        isOpen={selectedTicketId !== null}
        onClose={() => setSelectedTicketId(null)}
        ticketId={selectedTicketId}
      />
    </>
  );
};
