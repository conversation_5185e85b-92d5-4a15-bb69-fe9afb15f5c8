import { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'flowbite-react';
import { HiX } from 'react-icons/hi';
import { useMutation, useQuery } from '@apollo/client';
import {
  Gateway_RevertTransactionDocument,
  Gateway_TransactionDocument,
} from '@/graphql/generated/graphql';
import { toast } from 'react-toastify';
import { SpinnerLoading } from '@/components/globals/spinner-loading/spinner-loading';
import { StatusChip } from '@/components/globals';
import { getTransactionStatusChip, TransactionStatus } from './transaction-tab';
import { useRouter } from 'next/navigation';
import { CustomerDetailSection } from '../customer/components/customer-detail-section';
import {
  ProductListSection,
  ProductListSectionProps,
} from '../products/components/product-list-section';
import { GeneralDetailsSection } from '@/components/globals/general-details-section';
import {
  formatDateStringUnstable,
  formatToLocalDateString,
  message,
} from '@/components/shared/utils';
import { falsyString } from '@/lib/falsyString';
import { moneyFormat, moneyFormatString } from '@/lib/utils';
import { hashCustomerData } from '@/lib/hashUtils';
import { ChargeDetailSection } from '../customer/components/charge-section';
import { apolloClient } from '@/lib/graphql/ApolloClient';

type TransactionDetailsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  queryData: {
    transactionID: string;
    groupID: string;
  };
};
export const TransactionDetailsModal = ({
  isOpen,
  onClose,
  queryData,
}: TransactionDetailsModalProps) => {
  const { transactionID, groupID } = queryData;
  const router = useRouter();
  const { data, loading, error, refetch } = useQuery(Gateway_TransactionDocument, {
    variables: {
      input: {
        data: {
          transactionID,
        },
        groupID,
      },
    },
    skip: falsyString(transactionID) || falsyString(groupID),
  });

  const transactionData = data?.gateway_transaction;

  const transactionStatus: any = transactionData?.status ?? '';

  const [revertTransactionMutation, { loading: revertTransactionLoading }] = useMutation(
    Gateway_RevertTransactionDocument,
    {
      onCompleted: (_) => {
        toast.success(
          `Transaction ${transactionStatus === 'BATCHED' ? 'refunded' : 'voided'} successfully`,
        );
        onClose();
      },
      onError: (error) => {
        toast.error(message.api.errorUpdate(' transation', error.message));
      },
    },
  );

  const onRevertTransaction = async () => {
    try {
      await revertTransactionMutation({
        variables: {
          input: {
            data: {
              transactionID,
            },
            groupID,
          },
        },
      });
      await apolloClient.resetStore();
    } catch (e) {
      console.error('Revert transaction error: ', e);
    }
  };

  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleConfirmRevert = async () => {
    setShowConfirmation(false);
    await onRevertTransaction();
  };

  const handleCancelRevert = () => {
    setShowConfirmation(false);
  };

  useEffect(() => {
    refetch();
  }, [transactionID, groupID]);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const [status, label] = getTransactionStatusChip(
    (transactionData?.status ?? '') as unknown as TransactionStatus,
  );

  // @TODO: Need to improve this to cover all status
  const getHistoryColor = (status: string) => {
    if (status === 'declined') return 'red';
    if (status === 'pending_settlement') return 'gray';
    return 'green';
  };

  const showActionButton = useMemo(() => {
    if (transactionData?.status) {
      return ['CAPTURED', 'BATCHED'].includes(transactionData?.status);
    }
  }, [transactionData?.status]);

  return (
    <>
      <Modal show={isOpen} onClose={onClose} size="7xl">
        <SpinnerLoading isLoading={loading || revertTransactionLoading} />

        <div className="overflow-scroll p-6">
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button color="gray" size="sm" onClick={onClose}>
                Back
              </Button>
              <h2 className="text-2xl font-bold">Transaction Details</h2>
            </div>
            <Button color="gray" size="sm" onClick={onClose}>
              <HiX className="h-5 w-5" />
            </Button>
          </div>

          <div className="mb-6 flex items-start justify-between">
            <div>
              <div className="flex items-center space-x-2">
                <span className="font-semibold text-gray-500">#</span>
                <span className="text-xl font-bold">{transactionID}</span>
                <StatusChip variant={status} label={label} big />
              </div>
              <div className="text-gray-500">
                Date: {formatDateStringUnstable(transactionData?.date)}
              </div>
            </div>
            <div className="flex gap-7 text-right">
              <div className="mb-2 text-3xl font-bold">{moneyFormat(transactionData?.amount)}</div>
              <div className="flex gap-3 space-x-2">
                {/* NOTE: Hide for the summit
              <Button color="gray" size="sm">
                  View receipt
                </Button> 
                */}
                {showActionButton && (
                  <Button color="gray" size="sm" onClick={() => setShowConfirmation(true)}>
                    {transactionStatus === 'BATCHED' ? 'Refund' : 'Void'}
                  </Button>
                )}
              </div>
            </div>
          </div>
          {!loading && (
            <div className="mb-8 grid grid-cols-2 gap-8">
              <div>
                <GeneralDetailsSection
                  details={[
                    { label: 'Created by', value: transactionData?.createdBy },
                    { label: 'Auth Code', value: transactionData?.authCode },
                    { label: 'Payment type', value: transactionData?.paymentType },
                    { label: 'Entry Method', value: transactionData?.entryMethod },
                    { label: 'Method', value: transactionData?.method },
                    { label: 'Token Source', value: transactionData?.tokenSource },
                    {
                      label: 'Batch #',
                      onClick: () =>
                        router.push(
                          `/dashboard/reporting/batches?id=${transactionData?.batchID}&from=transactions`,
                        ),
                      value: transactionData?.batchID,
                    },
                    { label: 'GSA', value: transactionData?.gsa },
                    { label: 'Payment Plan', value: transactionData?.paymentPlan },
                    { label: 'EMV', value: transactionData?.emv },
                    { label: 'Source', value: transactionData?.source },
                    { label: 'Last 4', value: transactionData?.last4 },
                    { label: 'Message', value: transactionData?.message },
                    { label: 'Result', value: transactionData?.result },
                    // { label: 'Commercial Level', value: transactionData?.commercialLevel },
                  ]}
                />

                <div className="mt-8">
                  {(transactionData?.relatedTransactions?.length ?? 0) > 1 && (
                    <h3 className="mb-4 text-lg font-semibold">Related Transactions</h3>
                  )}
                  <div className="space-y-4">
                    {transactionData?.relatedTransactions?.map((data) => (
                      <HistoryItem
                        key={data?.transactionID}
                        title={data?.paymentType}
                        date={formatToLocalDateString(new Date(data?.date ?? ''))}
                        color={'green'}
                        details={[
                          {
                            label: 'ID',
                            value: (
                              <a
                                className="text-blue-500"
                                href={`/dashboard/reporting/?id=${data?.transactionID}`}
                              >
                                {data?.transactionID}
                              </a>
                            ),
                          },
                          {
                            label: 'Status',
                            value: data?.status,
                          },
                          {
                            label: 'Amout',
                            value: moneyFormatString(data?.amount),
                          },
                        ]}
                      />
                    ))}
                  </div>
                </div>

                <div className="mt-8">
                  <h3 className="mb-4 text-lg font-semibold">Transaction History</h3>
                  <div className="space-y-4">
                    {transactionData?.transactionHistory?.[0] && (
                      <HistoryItem
                        title="Authorization"
                        date={formatToLocalDateString(
                          new Date(transactionData?.transactionHistory?.[0].date ?? ''),
                        )}
                        color={getHistoryColor(
                          transactionData?.transactionHistory?.[0].status ?? '',
                        )}
                        details={[
                          {
                            label: 'Status',
                            value: transactionData?.transactionHistory?.[0].status,
                          },
                          {
                            label: 'Response',
                            value: transactionData?.transactionHistory?.[0].response,
                          },
                          {
                            label: 'AVS Response',
                            value: transactionData?.transactionHistory?.[0].avs,
                          },
                          { label: 'CVV', value: transactionData?.transactionHistory?.[0].cvv },
                        ]}
                      />
                    )}
                    {transactionData?.transactionHistory?.[1] && (
                      <HistoryItem
                        title={`Batch`}
                        date={formatToLocalDateString(
                          new Date(transactionData?.transactionHistory?.[1].date ?? ''),
                        )}
                        color={getHistoryColor(
                          transactionData?.transactionHistory?.[1].status ?? '',
                        )}
                        details={[
                          {
                            label: 'Status',
                            value: transactionData?.transactionHistory?.[1].status,
                          },
                        ]}
                      />
                    )}
                  </div>
                </div>
              </div>

              <div>
                <CustomerDetailSection
                  customer={{
                    customerID: transactionData?.customerID,
                    name: hashCustomerData.name(transactionData?.customerName),
                    email: hashCustomerData.email(transactionData?.customerEmail),
                    phone: hashCustomerData.phone(transactionData?.customerPhone),
                    country: transactionData?.customerCountry,
                    billingAddress: hashCustomerData.address(
                      transactionData?.customerBillingAddress,
                    ),
                  }}
                />

                <ChargeDetailSection
                  customer={{
                    customerID: transactionData?.customerID,
                  }}
                  charges={[
                    {
                      description: 'Subtotal',
                      amount: transactionData?.breakdown?.subtotal ?? 0,
                    },
                    {
                      description: 'Tax',
                      amount: transactionData?.breakdown?.tax ?? 0,
                    },
                    {
                      description: 'Tip',
                      amount: transactionData?.breakdown?.tip ?? 0,
                    },
                    {
                      description: 'Shipping',
                      amount: transactionData?.breakdown?.shipping ?? 0,
                    },
                    {
                      description: 'Extra Fees',
                      amount: transactionData?.breakdown?.fees ?? 0,
                    },
                    {
                      description: 'Discount',
                      amount: transactionData?.breakdown?.discount ?? 0,
                    },
                  ]}
                />

                <div>
                  <h3 className="mb-4 text-lg font-semibold">Purchase Details</h3>
                  <ProductListSection
                    products={
                      transactionData?.purchaseDetails as unknown as ProductListSectionProps['products']
                    }
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </Modal>

      <Modal show={showConfirmation} onClose={handleCancelRevert}>
        <Modal.Header>Confirm Action</Modal.Header>
        <Modal.Body>
          Are you sure you want to {transactionStatus === 'BATCHED' ? 'refund' : 'void'} this
          transaction?
        </Modal.Body>
        <Modal.Footer>
          <Button color="gray" onClick={handleCancelRevert}>
            Cancel
          </Button>
          <Button color="red" onClick={handleConfirmRevert}>
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

const HistoryItem = ({ title, date, details, color = 'green' }) => (
  <div className={`border-l-4 border-${color}-500 pl-4`}>
    <div className="mb-2 flex items-center justify-between">
      <h4 className="font-semibold">{title}</h4>
      <span className="text-sm text-gray-500">{date}</span>
    </div>
    <div className="space-y-1">
      {details.map((detail, index) => (
        <div key={index} className="text-sm">
          <span className="font-medium">{detail.label}:</span> {detail.value}
        </div>
      ))}
    </div>
  </div>
);
