'use client';

import { AUTHSTORE } from '@/lib/auth-storage';
import { env } from 'next-runtime-env';
import { redirect } from 'next/navigation';
import { useEffect } from 'react';

export default function Home() {
  useEffect(() => {
    let token = AUTHSTORE.get();

    if (token) {
      redirect('/dashboard');
    } else {
      if (env('NEXT_PUBLIC_DEMO') === 'true') {
        redirect('/demo');
      } else {
        redirect('/login');
      }
    }
  }, []);
  return <div></div>;
}
