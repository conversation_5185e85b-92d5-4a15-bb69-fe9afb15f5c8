'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { GetQuickUserInfo } from '@/graphql/declarations/user-members';

type UserDetailsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  userId?: string;
  inviteId?: string;
  groupId?: string;
};

export const UserDetailsModal = ({
  isOpen,
  onClose,
  userId,
  inviteId,
  groupId,
}: UserDetailsModalProps) => {
  console.log('UserDetailsModal - Render:', { isOpen, userId, inviteId, groupId });

  const isInvite = userId === 'invite';
  const isNewEntry = isInvite && !inviteId;

  const [selectedGroup, setSelectedGroup] = useState<string>(groupId || '');
  const [userFlags, setUserFlags] = useState<string[]>([]);
  const [adminInfo, setAdminInfo] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    title: '',
    email: '',
    phone: '',
  });

  // Fetch user data
  const { data: userData } = useQuery(GetQuickUserInfo, {
    variables: {
      where: {
        id: userId,
      },
    },
    skip: !userId || isInvite,
  });

  // Update form data when user data changes
  useEffect(() => {
    console.log('UserDetailsModal - User data changed:', userData);
    if (userData?.user) {
      setFormData({
        name: userData.user.name || '',
        title: userData.user.title || '',
        email: userData.user.email || '',
        phone: userData.user.phone || '',
      });
    }
  }, [userData]);

  const handleClose = () => {
    console.log('UserDetailsModal - Closing modal');
    if (onClose) onClose();
  };

  const handleSave = () => {
    console.log('UserDetailsModal - Saving:', formData);
    // TODO: Implement save logic
    handleClose();
  };

  console.log('UserDetailsModal - Current form data:', formData);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        console.log('Dialog open change:', open);
        if (!open) handleClose();
      }}
    >
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{userId === 'new' ? 'Add New User' : 'Edit User'}</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
              className="w-full rounded-md border p-2"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="title">Title</Label>
            <input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
              className="w-full rounded-md border p-2"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="email">Email</Label>
            <input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
              className="w-full rounded-md border p-2"
              disabled={isInvite && !isNewEntry}
            />
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
