import { StatusChip } from '@/components/globals';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { Gateway_BatchDocument, Gateway_CloseBatchDocument } from '@/graphql/generated/graphql';
import { useMutation, useQuery } from '@apollo/client';
import { Button, Modal } from 'flowbite-react';
import moment from 'moment';
import { useEffect, useMemo } from 'react';
import { HiX } from 'react-icons/hi';
import { toast } from 'react-toastify';
import { getTransactionStatusChip } from '../trasaction/transaction-tab';
import { BatchStatus, getBatchStatusChip } from './batches-tab';
import { useRouter } from 'next/navigation';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { formatToLocalDateString } from '@/components/shared/utils';
import { CardBrand } from '@/components/shared/components';
import { moneyFormat } from '@/lib/utils';

type BatchDetailsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  queryData: {
    batchID: string;
    groupID: string;
  };
};

export const BatchDetailsModal = ({ isOpen, onClose, queryData }: BatchDetailsModalProps) => {
  const router = useRouter();
  const { batchID, groupID } = queryData;
  const { data, loading, error, refetch } = useQuery(Gateway_BatchDocument, {
    variables: {
      input: {
        data: {
          batchID,
        },
        groupID,
      },
    },
    skip: !batchID || !groupID,
  });

  const batchData = data?.gateway_batch;

  const [closeBatchMutation, { loading: closingBatchLoading }] = useMutation(
    Gateway_CloseBatchDocument,
    {
      onCompleted: () => {
        toast.success('Batch Successfully closed!');
        onClose();
      },
      onError: (error) => {
        toast.error('Error closing the batch: ' + error.message);
      },
    },
  );

  useEffect(() => {
    refetch();
  }, [batchID, groupID]);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const transactionRows = useMemo(() => {
    if (!batchData?.transactions) return [];

    // Filter out null values and ensure all required fields are present
    return (batchData.transactions ?? []).filter(
      (transaction): transaction is NonNullable<typeof transaction> => {
        return transaction !== null;
      },
    );
  }, [batchData]);

  const columns: Column[] = [
    {
      key: 'transactionID',
      header: 'Transaction #',
      width: '90px',
      sortable: true,
      onClick: (row) => router.push(`/dashboard/reporting?id=${row.transactionID}`),
    },
    {
      key: 'date',
      header: 'Date',
      sortable: true,
      valueGetter: (row) => moment(row?.date).format('MM/DD/YYYY'),
    },
    {
      key: 'method',
      header: 'Method',
      sortable: true,
    },
    {
      key: 'customer',
      header: 'Name',
      width: '150px',
      sortable: true,
    },
    {
      key: 'last4',
      header: 'Last 4',
      sortable: true,
    },
    {
      key: 'amount',
      header: 'Amount',
      sortable: true,
      width: '40px',
    },
    {
      key: 'brand',
      header: 'Brand',
      width: '150px',
      sortable: true,
      renderCell: (row) => <CardBrand brand={row.brand} />,
    },
    {
      key: 'status',
      header: 'Status',
      width: '160px',
      renderCell: (row) => {
        const [status, label] = getTransactionStatusChip(row.status);
        return <StatusChip variant={status} label={label} />;
      },
    },
  ];

  const handleBatchClose = async () => {
    try {
      await closeBatchMutation({
        variables: {
          input: {
            groupID,
            data: {
              batchID,
            },
          },
        },
      });
    } catch (e) {
      console.error('Add Customer Mutation error: ', e);
    }
  };
  const isShowActionButtons = useMemo(() => {
    return batchData?.status !== '';
  }, [batchData]);
  const [status, label] = getBatchStatusChip((batchData?.status || '') as unknown as BatchStatus);
  return (
    <>
      <SpinnerLoading isLoading={closingBatchLoading} />
      <Modal show={isOpen} onClose={onClose} size="7xl">
        <div className="p-6">
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button color="gray" size="sm" onClick={onClose}>
                Back
              </Button>
              <h2 className="text-2xl font-bold">Batch Details</h2>
            </div>
            <Button color="gray" size="sm" onClick={onClose}>
              <HiX className="h-5 w-5" />
            </Button>
          </div>

          <div className="mb-6 flex items-start justify-between">
            <div>
              <div className="flex items-center space-x-2">
                <span className="font-semibold text-gray-500">#</span>
                <span className="text-xl font-bold">{batchID}</span>
                <StatusChip variant={status} label={label} big />
                <div className="border-bottom flex justify-between py-1 text-center text-sm">
                  <span className="font-medium">Location :</span>
                  <div className="flex items-center text-sm text-gray-500">
                    {batchData?.location}
                  </div>
                </div>
                <div className="border-bottom flex justify-between py-1 text-center text-sm">
                  <span className="font-medium">Date :</span>
                  <div className="flex items-center text-sm text-gray-500">
                    {formatToLocalDateString(new Date(batchData?.date ?? ''))}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex gap-7 text-right">
              <div className="mb-2 text-3xl font-bold">{moneyFormat(batchData?.amount)}</div>
              {/* <div className="flex gap-3 space-x-2">
                {isShowActionButtons && (
                  <Button
                    color="gray"
                    size="sm"
                    disabled={batchData?.status === ''}
                    onClick={handleBatchClose}
                  >
                    Close Batch
                  </Button>
                )}
              </div> */}
            </div>
          </div>
          <div>
            <DataGridView
              columns={columns}
              rows={transactionRows}
              isLoading={loading}
              disablePagination
            />
          </div>
        </div>
      </Modal>
    </>
  );
};
