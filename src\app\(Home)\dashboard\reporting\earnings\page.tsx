'use client';

import { <PERSON>Header } from '@/components/globals';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { moneyFormat } from '@/lib/utils';
import { useMemo, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { Badge } from '@/components/ui/badge';
import moment from 'moment';
import { Database, Download, Users } from 'lucide-react';
import { mockEarningsData } from '@/mock/earnings-data';
import { UserDetailsDialog } from '@/components/pages/dashboard/reporting/user-details-dialog';

interface DateRange {
  from: Date | undefined;
  to: Date | undefined;
}

export default function EarningsPage() {
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Transform data for the table
  const userData = useMemo(() => {
    return mockEarningsData.data.map((item) => ({
      id: item.user.id,
      partnerName: item.user.name,
      title: item.user.title || 'Partner User',
      lastLogin: moment(parseInt(item.user.lastLogin)).format('MMM DD, YYYY'),
      earnings: item.user.earnings || 0,
      accountsRecruited: item.merchant?.length || 0,
      activeAccounts: item.merchant?.filter((m) => m.status === 'active').length || 0,
      // Include merchant data for details dialog
      merchantData: item.merchant || [],
    }));
  }, []);

  // Calculate organization totals from mock data
  const organizationStats = useMemo(() => {
    return {
      totalEarnings: mockEarningsData.totalEarnings,
      totalWithdrawn: mockEarningsData.totalWithdrawn,
      totalWithdrawable: mockEarningsData.totalWithdrawable,
      totalUsers: mockEarningsData.data.length,
      totalAccounts: mockEarningsData.data.reduce(
        (sum, item) => sum + (item.merchant?.length || 0),
        0,
      ),
      activeAccounts: mockEarningsData.data.reduce(
        (sum, item) => sum + (item.merchant?.filter((m) => m.status === 'active').length || 0),
        0,
      ),
    };
  }, []);

  const handleRowClick = (row: any) => {
    setSelectedUserId(row.id);
    setIsDialogOpen(true);
  };

  // Column definitions for partner users table
  const columns: Column[] = [
    {
      key: 'partnerName',
      header: 'Partner User',
      width: '200px',
      onClick: handleRowClick,
    },
    {
      key: 'title',
      header: 'Title',
      width: '150px',
      onClick: handleRowClick,
    },
    {
      key: 'accountsRecruited',
      header: 'Total Accounts',
      width: '120px',
      onClick: handleRowClick,
    },
    {
      key: 'activeAccounts',
      header: 'Active Accounts',
      width: '120px',
      renderCell: (row) => (
        <Badge variant={row.activeAccounts > 0 ? 'default' : 'secondary'}>
          {row.activeAccounts}
        </Badge>
      ),
      onClick: handleRowClick,
    },
    {
      key: 'earnings',
      header: 'Total Earnings',
      width: '150px',
      renderCell: (row) => <span>{moneyFormat(row.earnings)}</span>,
      sortable: true,
      onClick: handleRowClick,
    },
    {
      key: 'lastLogin',
      header: 'Last Activity',
      width: '150px',
      onClick: handleRowClick,
    },
  ];

  // Find selected user data for dialog
  const selectedUser = useMemo(() => {
    if (!selectedUserId) return null;
    const originalUser = mockEarningsData.data.find((item) => item.user.id === selectedUserId);
    if (!originalUser) return null;
    return {
      user: originalUser.user,
      merchants: originalUser.merchant || [],
    };
  }, [selectedUserId]);

  return (
    <div className="space-y-6">
      <PageHeader text="Earnings & Partner Performance" />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="bg-white lg:col-span-1">
          <CardContent className="p-6">
            <div className="mb-4 w-fit rounded-full bg-blue-500/10 p-3">
              <Database className="h-6 w-6 text-blue-500" />
            </div>
            <div className="text-sm text-gray-600">Total Partner Earnings</div>
            <div className="mt-2 flex items-baseline">
              <div className="text-2xl font-bold text-blue-600">
                {moneyFormat(organizationStats.totalEarnings)}
              </div>
            </div>
            <div className="mt-2">
              <div className="flex justify-between text-sm">
                <span>Withdrawn</span>
                <span className="font-medium">{moneyFormat(organizationStats.totalWithdrawn)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Available</span>
                <span className="font-medium">
                  {moneyFormat(organizationStats.totalWithdrawable)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white lg:col-span-1">
          <CardContent className="p-6">
            <div className="mb-4 w-fit rounded-full bg-green-500/10 p-3">
              <Users className="h-6 w-6 text-green-500" />
            </div>
            <div className="text-sm text-gray-600">Partner Network</div>
            <div className="mt-2 flex items-baseline">
              <div className="text-2xl font-bold text-green-600">
                {organizationStats.totalUsers}
              </div>
              <div className="ml-2 text-sm text-gray-500">partner users</div>
            </div>
            <div className="mt-2">
              <div className="flex justify-between text-sm">
                <span>Total Accounts</span>
                <span className="font-medium">{organizationStats.totalAccounts}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Active</span>
                <span className="font-medium">{organizationStats.activeAccounts}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white lg:col-span-1">
          <CardContent className="p-6">
            <h3 className="font-medium">Quick Actions</h3>
            <div className="mt-4 space-y-3">
              <Button variant="outline" className="w-full justify-start" size="sm">
                <Download className="mr-2 h-4 w-4" />
                Download Earnings Report
              </Button>
              <Button variant="outline" className="w-full justify-start" size="sm">
                <Users className="mr-2 h-4 w-4" />
                View Partner Directory
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <h3 className="text-lg font-medium">Partner User Performance</h3>
        </CardHeader>
        <CardContent>
          <DataGridView
            columns={columns}
            rows={userData}
            pageSize={pageSize}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
            onPageSizeChange={setPageSize}
            totalRecords={userData.length}
            mode="client"
          />
        </CardContent>
      </Card>

      {/* User Details Dialog */}
      {selectedUser && (
        <UserDetailsDialog
          isOpen={isDialogOpen}
          onClose={() => {
            setIsDialogOpen(false);
            setSelectedUserId(null);
          }}
          user={selectedUser.user}
          merchants={selectedUser.merchants}
        />
      )}
    </div>
  );
}
