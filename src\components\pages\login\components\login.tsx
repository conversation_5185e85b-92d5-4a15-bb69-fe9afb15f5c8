'use client';

/* eslint-disable @next/next/no-img-element */
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Tit<PERSON> } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useForm } from 'react-hook-form';
import Link from 'next/link';
import { Button, Checkbox } from 'flowbite-react';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import React from 'react';
import { env } from 'next-runtime-env';

interface LoginPageProps {
  handleLogin: (data: { email: string; password: string; oneTime: boolean }) => void;
  isLoggingIn: boolean;
  error?: string;
  isBinding?: boolean;
  shouldBind?: boolean;
  setShouldBind?: (shouldBind: boolean) => void;
}

export const LoginPage: React.FC<LoginPageProps> = ({
  handleLogin,
  isLoggingIn,
  error,
  isBinding,
  setShouldBind,
  shouldBind,
}) => {
  const [rememberMe, setRememberMe] = useState(true);
  const [isInIframe, setIsInIframe] = useState(false);
  const query = useSearchParams();
  const idleSessionValue = query?.get('idleSession');
  const {
    register,
    formState: { errors },
    watch,
  } = useForm<{ email: string; password: string }>();

  const watchData = watch();

  const onSubmit = () => {
    handleLogin({
      email: watchData.email,
      password: watchData.password,
      oneTime: !rememberMe, // if rememberMe is false, then oneTime is true
    });
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsInIframe(window.self !== window.top);
    }
  }, []);

  return (
    <form>
      <CardHeader className="space-y-1">
        <CardTitle className="text-center text-2xl font-bold">
          <img src="/logo.webp" className="mx-auto h-16" alt="Logo" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          {idleSessionValue && (
            <>
              <div className="border-l-4 border-red-500 bg-red-50 pl-4">
                <div className="mb-2 flex items-center justify-between py-2 pr-2">
                  <p>
                    You have been logged out due to inactivity for a long time. Please log in again
                    to continue.
                  </p>
                </div>
              </div>
            </>
          )}
          <div className="font-inter text-2xl font-bold leading-7">Welcome back</div>
          {!isInIframe && (
            <div className="text-sm text-muted-foreground">
              Don't have an account?{' '}
              <Link
                href={env('NEXT_PUBLIC_SIGNUP_CUSTOM_URL') || '/register'}
                className="text-primary font-normal text-blue-600"
              >
                Sign up
              </Link>
            </div>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <div className="relative">
            <Input
              id="email"
              placeholder="<EMAIL>"
              type="email"
              {...register('email', { required: 'Email is required' })}
            />
          </div>
          {errors.email && <p className="text-xs text-red-600">{errors.email.message}</p>}
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <div className="relative">
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              {...register('password', { required: 'Password is required' })}
            />
          </div>
          {errors.password && <p className="text-xs text-red-600">{errors.password.message}</p>}
        </div>
        {!isInIframe && (
          <div>
            <Checkbox
              id="remember-me"
              checked={rememberMe}
              onChange={(e) => setRememberMe(e.target.checked)}
            />{' '}
            <Label htmlFor="remember-me">Remember me.</Label>
          </div>
        )}
        <Button className="w-full" onClick={onSubmit} color="blue" disabled={isLoggingIn}>
          {isLoggingIn ? 'Logging in...' : 'Sign In'}
        </Button>
        {error && <p className="-pt-2 mx-auto text-center text-xs text-red-600">{error}</p>}
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <div className="mb-4 flex w-full justify-between">
          {isBinding && (
            <div>
              <Checkbox
                id="link-access"
                checked={shouldBind}
                onChange={(e) => setShouldBind?.(e.target.checked)}
              />{' '}
              <Label htmlFor="link-access">Link User Access</Label>
            </div>
          )}
          <a href="/forgot-password" className="text-sm text-blue-600">
            Forgot password?
          </a>
        </div>
        <div className="mt-2 border-t pt-2 text-center">
          <div className="text-xs text-gray-500">
            <p>
              <strong>Email:</strong> <EMAIL>
            </p>
            <p>
              <strong>Password:</strong> QpLoXGy6y3
            </p>
          </div>
        </div>
      </CardFooter>
    </form>
  );
};
