import { GatewayUniDisputeOutput } from '@/graphql/generated/graphql';

// Helper function to generate random dates within a range
const getRandomDate = (start: Date, end: Date): Date => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// Helper to generate random transaction IDs
const generateTransactionId = (): string => {
  return `TXN-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
};

// Helper to generate case IDs
const generateCaseId = (): string => {
  return `CASE-${Math.floor(10000 + Math.random() * 90000)}`;
};

// Mock dispute statuses
const disputeStatuses = ['pending', 'representment', 'settled', 'declined'] as const;

// Mock dispute reasons
const disputeReasons = [
  'Fraudulent Transaction',
  'Product Not Received',
  'Product Unacceptable',
  'Duplicate Transaction',
  'Subscription Cancelled',
  'Credit Not Processed',
  'General',
] as const;

// Mock file types for documents
const fileTypes = [
  'Sales Receipt',
  'Proof of Delivery',
  'Transaction Document',
  'Authorization Document',
  'Chargeback Document',
  'Representment Document',
] as const;

// Interface for our mock dispute data
export interface MockDisputeData extends GatewayUniDisputeOutput {
  caseID: string;
  customer: string;
  date: string;
  reason: string;
  amount: number;
  transactionID: string;
  hasChallenged: boolean;
  daysToRepresent: number;
  status: (typeof disputeStatuses)[number];
  location: string;
  createdBy: string;
  authCode: string;
  paymentType: string;
  entryMethod: string;
  method: string;
  tokenSource: string;
  batchID: string;
  gsa: string;
  paymentPlan: string | null;
  emv: string;
  source: string;
  last4: string;
  disputeID: string;
  name: string;
  email: string;
  phone: string;
  country: string;
  billingAddress: string;
  files: Array<{
    id: string;
    fileUrl: string;
    fileFormat: string;
    purpose: string;
    size: number;
    createdAt: string;
    submittedAt: string | null;
  }>;
  history: Array<{
    action: string;
    body: string;
    actor: string;
    createdAt: string;
    id: string;
  }>;
  products?: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
  }>;
}

// Generate a single mock dispute
const generateMockDispute = (): MockDisputeData => {
  const now = new Date();
  const startDate = new Date(now.getFullYear(), now.getMonth() - 3, 1);
  const disputeDate = getRandomDate(startDate, now);
  const hasChallenged = Math.random() > 0.5;
  const status = disputeStatuses[Math.floor(Math.random() * disputeStatuses.length)];
  const daysToRepresent = status === 'pending' ? Math.floor(Math.random() * 30) + 1 : 0;
  const caseId = generateCaseId();

  const mockFiles = Array(Math.floor(Math.random() * 3) + 1)
    .fill(null)
    .map((_, index) => ({
      id: `FILE-${Math.random().toString(36).substr(2, 9)}`,
      fileUrl: `https://example.com/files/${Math.random().toString(36).substr(2, 9)}.pdf`,
      fileFormat: 'application/pdf',
      purpose: fileTypes[Math.floor(Math.random() * fileTypes.length)],
      size: Math.floor(Math.random() * 1000000) + 100000,
      createdAt: new Date(disputeDate.getTime() + 1000 * 60 * 60 * 24 * index).toISOString(),
      submittedAt: hasChallenged
        ? new Date(disputeDate.getTime() + 1000 * 60 * 60 * 24 * (index + 1)).toISOString()
        : null,
    }));

  const mockHistory = [
    {
      action: 'DISPUTE_CREATED',
      body: JSON.stringify({
        status: 'initiated',
        amount: Math.floor(Math.random() * 100000) + 1000,
      }),
      actor: 'System',
      createdAt: disputeDate.toISOString(),
      id: `HIST-${Math.random().toString(36).substr(2, 9)}`,
    },
    ...(hasChallenged
      ? [
          {
            action: 'CHALLENGE_SUBMITTED',
            body: JSON.stringify({
              challenge: {
                status: 'SUCCESS',
                document_name: 'Transaction Receipt',
                action: {
                  type: 'DOCUMENT_UPLOAD',
                  time_created: new Date(disputeDate.getTime() + 1000 * 60 * 60 * 24).toISOString(),
                  result_code: 'SUCCESS',
                },
              },
            }),
            actor: 'Merchant',
            createdAt: new Date(disputeDate.getTime() + 1000 * 60 * 60 * 24).toISOString(),
            id: `HIST-${Math.random().toString(36).substr(2, 9)}`,
          },
        ]
      : []),
    ...(!hasChallenged && daysToRepresent < 5
      ? [
          {
            action: 'DAYS_TO_REPRESENT_ZERO',
            body: JSON.stringify({ daysToRepresent }),
            actor: 'System',
            createdAt: new Date().toISOString(),
            id: `HIST-${Math.random().toString(36).substr(2, 9)}`,
          },
        ]
      : []),
  ];

  return {
    caseID: caseId,
    customer: `Customer ${Math.floor(Math.random() * 1000)}`,
    date: disputeDate.toISOString(),
    reason: disputeReasons[Math.floor(Math.random() * disputeReasons.length)],
    amount: Math.floor(Math.random() * 100000) + 1000, // Amount in cents between $10 and $1000
    transactionID: generateTransactionId(),
    hasChallenged,
    daysToRepresent,
    status,
    location: `Location ${Math.floor(Math.random() * 10) + 1}`,
    createdBy: 'John Doe',
    authCode: Math.random().toString(36).substr(2, 6).toUpperCase(),
    paymentType: 'Credit Card',
    entryMethod: 'Online',
    method: 'VISA',
    tokenSource: 'Gateway',
    batchID: `BATCH-${Math.floor(Math.random() * 10000)}`,
    gsa: 'GSA001',
    paymentPlan: Math.random() > 0.7 ? `PLAN-${Math.floor(Math.random() * 1000)}` : null,
    emv: 'Yes',
    source: 'Web',
    last4: Math.floor(1000 + Math.random() * 9000).toString(),
    disputeID: caseId,
    name: `John Doe ${Math.floor(Math.random() * 100)}`,
    email: `customer${Math.floor(Math.random() * 100)}@example.com`,
    phone: `+1${Math.floor(Math.random() * 10000000000)}`,
    country: 'United States',
    billingAddress: '123 Main St, Anytown, USA',
    files: mockFiles,
    history: mockHistory,
    products: Array(Math.floor(Math.random() * 3) + 1)
      .fill(null)
      .map(() => ({
        id: `PROD-${Math.random().toString(36).substr(2, 9)}`,
        name: `Product ${Math.floor(Math.random() * 100)}`,
        quantity: Math.floor(Math.random() * 5) + 1,
        price: Math.floor(Math.random() * 10000) + 500,
      })),
  };
};

// Generate an array of mock disputes
export const generateMockDisputes = (count: number = 50): MockDisputeData[] => {
  return Array(count).fill(null).map(generateMockDispute);
};

// Create a static set of mock disputes with deterministic values
export const mockDisputes: MockDisputeData[] = [
  {
    caseID: 'CASE-99767',
    customer: 'Customer 001',
    date: '2025-06-01T14:30:00Z',
    reason: 'Fraudulent Transaction',
    amount: 150000, // $1,500.00
    transactionID: 'TXN-9XK42P8M7',
    hasChallenged: false,
    daysToRepresent: 3, // Urgent case
    status: 'pending',
    location: 'NYC Store',
    createdBy: 'Sarah Wilson',
    authCode: 'AX729B',
    paymentType: 'Credit Card',
    entryMethod: 'Chip Reader',
    method: 'VISA',
    tokenSource: 'Gateway',
    batchID: 'BATCH-8842',
    gsa: 'GSA732',
    paymentPlan: null,
    emv: 'Yes',
    source: 'POS',
    last4: '4242',
    disputeID: 'CASE-99767',
    name: 'Alex Thompson',
    email: '<EMAIL>',
    phone: '+12125550123',
    country: 'United States',
    billingAddress: '742 Broadway, New York, NY 10003',
    files: [
      {
        id: 'FILE-99A',
        fileUrl: 'https://example.com/files/receipt-99767.pdf',
        fileFormat: 'application/pdf',
        purpose: 'Transaction Document',
        size: 245000,
        createdAt: '2025-06-01T14:35:00Z',
        submittedAt: null,
      },
    ],
    history: [
      {
        action: 'DISPUTE_CREATED',
        body: JSON.stringify({
          status: 'initiated',
          amount: 150000,
        }),
        actor: 'System',
        createdAt: '2025-06-01T14:30:00Z',
        id: 'HIST-99A',
      },
      {
        action: 'DAYS_TO_REPRESENT_ZERO',
        body: JSON.stringify({ daysToRepresent: 3 }),
        actor: 'System',
        createdAt: '2025-06-01T14:35:00Z',
        id: 'HIST-99B',
      },
    ],
    products: [
      {
        id: 'PROD-99A',
        name: 'Designer Watch',
        quantity: 1,
        price: 150000,
      },
    ],
  },
  {
    caseID: 'CASE-99768',
    customer: 'Customer 002',
    date: '2025-05-30T09:15:00Z',
    reason: 'Product Not Received',
    amount: 89900, // $899.00
    transactionID: 'TXN-7HJ23K4L5',
    hasChallenged: true,
    daysToRepresent: 0,
    status: 'representment',
    location: 'Online Store',
    createdBy: 'Michael Chen',
    authCode: 'BX447C',
    paymentType: 'Credit Card',
    entryMethod: 'Online',
    method: 'MASTERCARD',
    tokenSource: 'Gateway',
    batchID: 'BATCH-8843',
    gsa: 'GSA733',
    paymentPlan: 'PLAN-445',
    emv: 'No',
    source: 'Web',
    last4: '5999',
    disputeID: 'CASE-99768',
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    phone: '+13105550199',
    country: 'United States',
    billingAddress: '8500 Beverly Blvd, Los Angeles, CA 90048',
    files: [
      {
        id: 'FILE-99B',
        fileUrl: 'https://example.com/files/shipping-99768.pdf',
        fileFormat: 'application/pdf',
        purpose: 'Proof of Delivery',
        size: 325000,
        createdAt: '2025-05-30T09:20:00Z',
        submittedAt: '2025-05-30T10:15:00Z',
      },
    ],
    history: [
      {
        action: 'DISPUTE_CREATED',
        body: JSON.stringify({
          status: 'initiated',
          amount: 89900,
        }),
        actor: 'System',
        createdAt: '2025-05-30T09:15:00Z',
        id: 'HIST-99C',
      },
      {
        action: 'CHALLENGE_SUBMITTED',
        body: JSON.stringify({
          challenge: {
            status: 'SUCCESS',
            document_name: 'Shipping Confirmation',
            action: {
              type: 'DOCUMENT_UPLOAD',
              time_created: '2025-05-30T10:15:00Z',
              result_code: 'SUCCESS',
            },
          },
        }),
        actor: 'Merchant',
        createdAt: '2025-05-30T10:15:00Z',
        id: 'HIST-99D',
      },
    ],
    products: [
      {
        id: 'PROD-99B',
        name: 'Wireless Headphones',
        quantity: 1,
        price: 89900,
      },
    ],
  },
  {
    caseID: 'CASE-99769',
    customer: 'Customer 003',
    date: '2025-05-25T16:45:00Z',
    reason: 'Duplicate Transaction',
    amount: 45000, // $450.00
    transactionID: 'TXN-5NM67P8Q9',
    hasChallenged: true,
    daysToRepresent: 0,
    status: 'settled',
    location: 'Chicago Store',
    createdBy: 'David Park',
    authCode: 'CX885D',
    paymentType: 'Credit Card',
    entryMethod: 'Contactless',
    method: 'VISA',
    tokenSource: 'Gateway',
    batchID: 'BATCH-8844',
    gsa: 'GSA734',
    paymentPlan: null,
    emv: 'Yes',
    source: 'POS',
    last4: '7123',
    disputeID: 'CASE-99769',
    name: 'Rachel Green',
    email: '<EMAIL>',
    phone: '+13125550177',
    country: 'United States',
    billingAddress: '401 N Michigan Ave, Chicago, IL 60611',
    files: [
      {
        id: 'FILE-99C',
        fileUrl: 'https://example.com/files/transaction-99769.pdf',
        fileFormat: 'application/pdf',
        purpose: 'Transaction Document',
        size: 198000,
        createdAt: '2025-05-25T16:50:00Z',
        submittedAt: '2025-05-25T17:30:00Z',
      },
    ],
    history: [
      {
        action: 'DISPUTE_CREATED',
        body: JSON.stringify({
          status: 'initiated',
          amount: 45000,
        }),
        actor: 'System',
        createdAt: '2025-05-25T16:45:00Z',
        id: 'HIST-99E',
      },
      {
        action: 'CHALLENGE_SUBMITTED',
        body: JSON.stringify({
          challenge: {
            status: 'SUCCESS',
            document_name: 'Transaction Records',
            action: {
              type: 'DOCUMENT_UPLOAD',
              time_created: '2025-05-25T17:30:00Z',
              result_code: 'SUCCESS',
            },
          },
        }),
        actor: 'Merchant',
        createdAt: '2025-05-25T17:30:00Z',
        id: 'HIST-99F',
      },
      {
        action: 'DISPUTE_SETTLED',
        body: JSON.stringify({
          status: 'settled',
          settlement_amount: 45000,
          settlement_date: '2025-06-01T12:00:00Z',
        }),
        actor: 'System',
        createdAt: '2025-06-01T12:00:00Z',
        id: 'HIST-99G',
      },
    ],
    products: [
      {
        id: 'PROD-99C',
        name: 'Smart Speaker',
        quantity: 1,
        price: 45000,
      },
    ],
  },
];
