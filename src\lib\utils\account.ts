import { AccountData } from '../mock/accounts-data';
import { AccountDetailsData } from '../mock/account-details-data';

export function formatAccountDetails(account: AccountData): AccountDetailsData {
  return {
    id: account.id,
    groupID: `GRP-${account.id}`,
    dbaName: account.name,
    friendlyName: account.name,
    accountStatus: account.status,
    dateAdded: account.lastActive,
    paymentType: account.status === 'active' ? 'enabled' : 'disabled',
    email: account.email,
    phone: account.phoneNumber,
    country: 'United States',
    billingAddress: '123 Main Street, Suite 100, City, ST 12345',
    metrics: {
      monthlySales: Math.round(account.totalEarnings / 12),
      closedTransactions: Math.round(account.totalEarnings / 100),
      availableBalance: Math.round(account.totalEarnings * 0.15),
    },
    limits: {
      cardPerTransaction: 5000,
      cardMonthly: 50000,
      cardMonthlyPercentage: 45,
      bankTransferPerTransaction: 10000,
      bankTransferMonthly: 100000,
      bankTransferMonthlyPercentage: 25,
    },
    bankInfo: {
      bankName: 'JPMORGAN CHASE BANK, NA',
      accountLast4: '4321',
      bankCode: '*********',
    },
  };
}
