// Comprehensive mock data for all GraphQL operations
import { mockDisputes } from '@/mock/disputes-data';
import { mockSupportTickets } from '@/lib/mock/support-mock';
import { mockAccounts } from '@/lib/mock/accounts-data';

// Mock user data
export const mockUser = {
  id: 'user-123',
  name: '<PERSON>',
  lastName: '<PERSON><PERSON>',
  displayName: '<PERSON>',
  email: '<EMAIL>',
  phone: '+****************',
  title: 'Partner Manager',
  role: 'ADMIN',
  createdAt: '2024-01-01T00:00:00Z',
  groupsCount: 5,
  flag_canAffiliate: true,
};

// Mock groups data
export const mockGroups = [
  {
    id: 'group-1',
    name: 'Main Business Group',
    labelName: 'Main Business',
    actualName: 'Main Business LLC',
    membersCount: 3,
    processorStatus: 'ACTIVE',
    signingURL: 'https://example.com/sign',
    mainProcessor: 'STRIPE',
    mainGateway: 'STRIPE_GATEWAY',
    einNumber: '12-3456789',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    displayMerchantID: 'MERCH-001',
    pciStatus: 'COMPLIANT',
    firstName: 'John',
    lastName: 'Doe',
    fullName: 'John Doe',
    addressLine1: '123 Main St',
    addressLine2: 'Suite 100',
    city: 'New York',
    state: 'NY',
    country: 'US',
    zip: '10001',
    mccCode: '5999',
    phoneNumber: '+****************',
    email: '<EMAIL>',
    default_includeSurcharge: true,
    default_globalDisableCC: false,
    default_globalDisableACH: false,
    ghlAccessesCount: 2,
    hubspotAccessesCount: 1,
    ghlPayTransactionMapsCount: 150,
    supportTicketsCount: 3,
    serviceAccountsCount: 2,
    flag_disableTokens: false,
    flag_disableAutoToken: false,
  },
  {
    id: 'group-2',
    name: 'Secondary Business',
    labelName: 'Secondary',
    actualName: 'Secondary Business Inc',
    membersCount: 2,
    processorStatus: 'PENDING',
    signingURL: 'https://example.com/sign2',
    mainProcessor: 'SQUARE',
    mainGateway: 'SQUARE_GATEWAY',
    einNumber: '98-7654321',
    createdAt: '2024-02-01T00:00:00Z',
    updatedAt: '2024-02-15T00:00:00Z',
    displayMerchantID: 'MERCH-002',
    pciStatus: 'PENDING',
    firstName: 'Jane',
    lastName: 'Smith',
    fullName: 'Jane Smith',
    addressLine1: '456 Oak Ave',
    addressLine2: '',
    city: 'Los Angeles',
    state: 'CA',
    country: 'US',
    zip: '90210',
    mccCode: '7299',
    phoneNumber: '+****************',
    email: '<EMAIL>',
    default_includeSurcharge: false,
    default_globalDisableCC: false,
    default_globalDisableACH: true,
    ghlAccessesCount: 1,
    hubspotAccessesCount: 0,
    ghlPayTransactionMapsCount: 75,
    supportTicketsCount: 1,
    serviceAccountsCount: 1,
    flag_disableTokens: false,
    flag_disableAutoToken: true,
  },
];

// Mock transactions data
export const mockTransactions = [
  {
    transactionID: 'TXN-001',
    status: 'COMPLETED',
    date: '2024-01-15T10:30:00Z',
    createdBy: 'John Doe',
    paymentType: 'CREDIT_CARD',
    method: 'VISA',
    batchID: 'BATCH-001',
    paymentPlan: null,
    source: 'ONLINE',
    authCode: 'AUTH123',
    amount: 15000, // $150.00
    entryMethod: 'KEYED',
    tokenSource: 'GATEWAY',
    gsa: 'GSA001',
    emv: 'NO',
    last4: '4242',
    customerName: 'Alice Johnson',
    customerID: 'CUST-001',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    customerCountry: 'US',
    customerBillingAddress: '789 Pine St, Seattle, WA 98101',
    commercialLevel: 'LEVEL_1',
    result: 'APPROVED',
    message: 'Transaction approved',
    brandReference: 'BRAND-REF-001',
    breakdown: {
      discount: 0,
      directDiscount: 0,
      actualDiscount: 0,
      tax: 1200, // $12.00
      shipping: 500, // $5.00
      shippingDiscount: 0,
      shippingDirectDiscount: 0,
      shippingActualDiscount: 0,
      fees: 450, // $4.50
      actualFees: 450,
      tip: 300, // $3.00
      subtotal: 13800, // $138.00
      subscriptionTotal: 0,
      rawTotal: 15000,
      total: 15000,
      expectedTotal: 15000,
    },
    transactionHistory: [
      {
        date: '2024-01-15T10:30:00Z',
        status: 'COMPLETED',
        response: 'APPROVED',
        avs: 'Y',
        cvv: 'M',
      },
    ],
  },
];

// Mock products data
export const mockProducts = [
  {
    id: 'PROD-001',
    name: 'Premium Service Package',
    price: 9999, // $99.99
    discount: 0,
    productStatus: 'ACTIVE',
    taxExempt: false,
    kitchenItem: false,
    sku: 'PSP-001',
    category: 'Services',
    subCategory: 'Premium',
    brand: 'NGnair',
    isRecurring: true,
    recurringMode: 'SUBSCRIPTION',
    recurringInterval: 'MONTHLY',
    recurringFrequency: 1,
    recurringTotalCycles: 0, // Unlimited
    recurringTrialDays: 7,
    recurringSetupFee: 2500, // $25.00
    description: 'Premium service package with all features included',
    isInStore: true,
    isOnline: true,
    productImages: [
      {
        url: '/images/products/premium-package.jpg',
        name: 'Premium Package Image',
      },
    ],
  },
  {
    id: 'PROD-002',
    name: 'Basic Service',
    price: 2999, // $29.99
    discount: 500, // $5.00 discount
    productStatus: 'ACTIVE',
    taxExempt: false,
    kitchenItem: false,
    sku: 'BS-001',
    category: 'Services',
    subCategory: 'Basic',
    brand: 'NGnair',
    isRecurring: false,
    recurringMode: null,
    recurringInterval: null,
    recurringFrequency: null,
    recurringTotalCycles: null,
    recurringTrialDays: null,
    recurringSetupFee: null,
    description: 'Basic one-time service',
    isInStore: true,
    isOnline: true,
    productImages: [
      {
        url: '/images/products/basic-service.jpg',
        name: 'Basic Service Image',
      },
    ],
  },
];

// Mock categories data
export const mockCategories = [
  {
    id: 'CAT-001',
    name: 'Services',
    status: 'ACTIVE',
    color: '#3B82F6',
    description: 'Service-based products',
    subCategory: ['Premium', 'Basic', 'Enterprise'],
    colors: ['#3B82F6', '#10B981', '#F59E0B'],
  },
  {
    id: 'CAT-002',
    name: 'Products',
    status: 'ACTIVE',
    color: '#10B981',
    description: 'Physical products',
    subCategory: ['Electronics', 'Accessories', 'Software'],
    colors: ['#10B981', '#8B5CF6', '#EF4444'],
  },
];

// Mock dashboard summary data - matches GraphQL schema
export const mockDashboardSummary = {
  dateStart: '2024-01-01',
  dateEnd: '2024-01-31',
  totalPortfolio: 45000.0,
  captured: {
    total: 32000.0,
    percentageChange: '15',
    timeStart: '2024-01-01T00:00:00Z',
    timeEnd: '2024-01-31T23:59:59Z',
    timeEntries: [
      { time: '2024-01-01T00:00:00Z', value: 1000 },
      { time: '2024-01-07T00:00:00Z', value: 1200 },
      { time: '2024-01-14T00:00:00Z', value: 1500 },
      { time: '2024-01-21T00:00:00Z', value: 1800 },
      { time: '2024-01-28T00:00:00Z', value: 2000 },
    ],
  },
  refunds: {
    total: 3000.0,
    percentageChange: '5',
    timeStart: '2024-01-01T00:00:00Z',
    timeEnd: '2024-01-31T23:59:59Z',
    timeEntries: [
      { time: '2024-01-01T00:00:00Z', value: 100 },
      { time: '2024-01-07T00:00:00Z', value: 150 },
      { time: '2024-01-14T00:00:00Z', value: 200 },
      { time: '2024-01-21T00:00:00Z', value: 250 },
      { time: '2024-01-28T00:00:00Z', value: 300 },
    ],
  },
  batched: {
    total: 28000.0,
    percentageChange: '10',
    timeStart: '2024-01-01T00:00:00Z',
    timeEnd: '2024-01-31T23:59:59Z',
    timeEntries: [
      { time: '2024-01-01T00:00:00Z', value: 900 },
      { time: '2024-01-07T00:00:00Z', value: 1100 },
      { time: '2024-01-14T00:00:00Z', value: 1400 },
      { time: '2024-01-21T00:00:00Z', value: 1700 },
      { time: '2024-01-28T00:00:00Z', value: 1900 },
    ],
  },
  deposits: {
    total: 25000.0,
    percentageChange: '12',
    timeStart: '2024-01-01T00:00:00Z',
    timeEnd: '2024-01-31T23:59:59Z',
    timeEntries: [
      { time: '2024-01-01T00:00:00Z', value: 800 },
      { time: '2024-01-07T00:00:00Z', value: 1000 },
      { time: '2024-01-14T00:00:00Z', value: 1300 },
      { time: '2024-01-21T00:00:00Z', value: 1600 },
      { time: '2024-01-28T00:00:00Z', value: 1800 },
    ],
  },
};

// Mock location summary data - matches GraphQL schema
export const mockLocationSummary = {
  data: [
    {
      locationID: 'LOC-001',
      locationName: 'Main Store',
      changePercentage: '15.5',
      currentYearTotal: '75000.25',
      lastYearTotal: '65000.00',
      yearChangePercentage: '15.4',
    },
    {
      locationID: 'LOC-002',
      locationName: 'Online Store',
      changePercentage: '12.3',
      currentYearTotal: '50000.25',
      lastYearTotal: '44500.00',
      yearChangePercentage: '12.4',
    },
    {
      locationID: 'LOC-003',
      locationName: 'Mobile Sales',
      changePercentage: '8.7',
      currentYearTotal: '25000.00',
      lastYearTotal: '23000.00',
      yearChangePercentage: '8.7',
    },
  ],
};

// Mock affiliation data
export const mockAffiliation = {
  id: 'AFF-001',
  affiliatesCount: 25,
  groupAffiliatesCount: 5,
  codesCount: 10,
  bank_routingNumber: '*********',
  bank_accountNumber: '****1234',
  bank_accountName: 'NGnair Business Account',
  codes: [
    { code: 'PARTNER10', description: '10% partner discount' },
    { code: 'NEWUSER20', description: '20% new user discount' },
    { code: 'BULK50', description: '50% bulk order discount' },
  ],
};

// Mock payment methods data
export const mockPaymentMethods = [
  {
    id: 'PM-001',
    type: 'CREDIT_CARD',
    brand: 'VISA',
    last4: '4242',
    expiryMonth: 12,
    expiryYear: 2025,
    isDefault: true,
    customerID: 'CUST-001',
  },
  {
    id: 'PM-002',
    type: 'BANK_ACCOUNT',
    bankName: 'Chase Bank',
    accountLast4: '1234',
    routingNumber: '*********',
    isDefault: false,
    customerID: 'CUST-001',
  },
];

// Mock customers data
export const mockCustomers = [
  {
    id: 'CUST-001',
    name: 'Alice Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Pine St, Seattle, WA 98101',
    country: 'US',
    totalSpent: 45000, // $450.00
    totalTransactions: 15,
    lastTransactionDate: '2024-01-15T10:30:00Z',
    status: 'ACTIVE',
    paymentMethods: mockPaymentMethods,
  },
  {
    id: 'CUST-002',
    name: 'Bob Smith',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Oak Ave, Portland, OR 97201',
    country: 'US',
    totalSpent: 28500, // $285.00
    totalTransactions: 8,
    lastTransactionDate: '2024-01-10T14:20:00Z',
    status: 'ACTIVE',
    paymentMethods: [],
  },
];

// Mock subscription data
export const mockSubscriptions = [
  {
    id: 'SUB-001',
    planId: 'PLAN-PREMIUM',
    status: 'ACTIVE',
    description: 'Premium Monthly Plan',
    customerId: 'CUST-001',
    amount: 9999, // $99.99
    billingCycleInterval: 'MONTHLY',
    billingFrequency: 1,
    nextBillDate: '2024-02-15T00:00:00Z',
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
  },
];

// Mock webhook data
export const mockWebhooks = [
  {
    id: 'WH-001',
    url: 'https://example.com/webhook',
    events: ['transaction.completed', 'transaction.failed'],
    status: 'ACTIVE',
    secret: 'whsec_mock_secret',
    createdAt: '2024-01-01T00:00:00Z',
  },
];

// Mock API keys data
export const mockApiKeys = [
  {
    id: 'KEY-001',
    name: 'Production API Key',
    keyPrefix: 'pk_live_',
    permissions: ['read', 'write'],
    status: 'ACTIVE',
    lastUsed: '2024-01-15T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'KEY-002',
    name: 'Test API Key',
    keyPrefix: 'pk_test_',
    permissions: ['read'],
    status: 'ACTIVE',
    lastUsed: '2024-01-14T16:20:00Z',
    createdAt: '2024-01-01T00:00:00Z',
  },
];

// Mock notifications data
export const mockNotifications = [
  {
    id: 'NOTIF-001',
    topics: ['TRANSACTION', 'PAYMENT'],
    read: false,
    content: 'New transaction completed for $150.00',
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 'NOTIF-002',
    topics: ['SYSTEM', 'UPDATE'],
    read: false,
    content: 'System maintenance scheduled for tonight',
    createdAt: '2024-01-15T08:00:00Z',
  },
  {
    id: 'NOTIF-003',
    topics: ['DISPUTE', 'ALERT'],
    read: true,
    content: 'Dispute case #DSP-001 has been resolved',
    createdAt: '2024-01-14T16:45:00Z',
  },
  {
    id: 'NOTIF-004',
    topics: ['ACCOUNT', 'SECURITY'],
    read: true,
    content: 'New login detected from Chrome browser',
    createdAt: '2024-01-14T14:20:00Z',
  },
  {
    id: 'NOTIF-005',
    topics: ['PAYMENT', 'SUCCESS'],
    read: true,
    content: 'Monthly subscription payment processed successfully',
    createdAt: '2024-01-13T09:15:00Z',
  },
];

// Export all mock data
export const mockGraphQLData = {
  user: mockUser,
  groups: mockGroups,
  transactions: mockTransactions,
  products: mockProducts,
  categories: mockCategories,
  disputes: mockDisputes,
  supportTickets: mockSupportTickets,
  accounts: mockAccounts,
  dashboardSummary: mockDashboardSummary,
  locationSummary: mockLocationSummary,
  affiliation: mockAffiliation,
  paymentMethods: mockPaymentMethods,
  customers: mockCustomers,
  subscriptions: mockSubscriptions,
  webhooks: mockWebhooks,
  apiKeys: mockApiKeys,
  notifications: mockNotifications,
};
