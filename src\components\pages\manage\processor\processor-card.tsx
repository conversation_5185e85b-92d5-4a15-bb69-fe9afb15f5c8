'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Processor } from '@/types/processors';
import Image from 'next/image';
import { useState } from 'react';
import { ProcessorConfigModal } from './processor-config-modal';

interface ProcessorCardProps {
  processor: Processor;
  onToggle: (enabled: boolean) => void;
}

export function ProcessorCard({ processor, onToggle }: ProcessorCardProps) {
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);

  return (
    <>
      <Card className="hover:bg-accent/5">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6">
          <div className="flex items-center space-x-6">
            {processor.logo && (
              <div className="relative flex h-20 w-32 items-center justify-center overflow-hidden rounded-xl bg-white p-3 shadow-sm">
                <Image
                  src={processor.logo}
                  alt={processor.name}
                  width={120}
                  height={80}
                  className="max-h-full max-w-full object-contain"
                  style={{
                    objectFit: 'contain',
                    width: 'auto',
                    height: 'auto',
                  }}
                />
              </div>
            )}{' '}
            <div>
              <CardTitle className="text-xl font-medium">{processor.name}</CardTitle>
              <CardDescription className="mt-1 text-sm text-muted-foreground">
                {processor.type}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <Button
              variant="outline"
              size="lg"
              onClick={() => setIsConfigModalOpen(true)}
              className="px-6"
            >
              Configure
            </Button>
            <Switch className="h-6 w-12" checked={processor.enabled} onCheckedChange={onToggle} />
          </div>
        </CardHeader>
<<<<<<< Updated upstream
        <CardContent></CardContent>
=======
        <CardContent className="px-6 pb-6">
          {processor.stats && (
            <div className="grid grid-cols-4 gap-4">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-medium">
                  ${processor.stats.revenue.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Transactions</p>
                <p className="text-2xl font-medium">
                  {processor.stats.totalTransactions.toLocaleString()}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-medium">
                  {(
                    (processor.stats.successfulTransactions / processor.stats.totalTransactions) *
                    100
                  ).toFixed(1)}
                  %
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">
                  {processor.type === 'CRM' ? 'Active Customers' : 'Conversion Rate'}
                </p>
                <p className="text-2xl font-medium">
                  {processor.type === 'CRM'
                    ? processor.stats.activeCustomers?.toLocaleString()
                    : processor.stats.conversionRate?.toFixed(1) + '%'}
                </p>
              </div>
            </div>
          )}
          {processor.config && (
            <div className="mt-4 rounded-lg border bg-muted/20 px-4 py-2">
              <p className="text-sm text-muted-foreground">Connected</p>
              <p className="mt-1 text-sm">
                Last sync: {processor.stats?.lastSync?.toLocaleString()}
              </p>
            </div>
          )}
        </CardContent>
>>>>>>> Stashed changes
      </Card>
      <ProcessorConfigModal
        isOpen={isConfigModalOpen}
        onClose={() => setIsConfigModalOpen(false)}
        processor={processor}
        onSave={(config) => {
          console.log('Saving config for', processor.name, config);
          // TODO: Implement saving configuration
        }}
      />
    </>
  );
}
