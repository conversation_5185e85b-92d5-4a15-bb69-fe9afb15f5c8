'use client';

// Import directly from the admin directory to reuse the component
import IntegrationList from '../../admin/integrations/_components/integration-list';

export default function Integrations() {
  return (
    <div className="py-5">
      <div className="flex flex-col gap-1 pb-5">
        <h1 className="text-2xl font-semibold text-slate-800">Integrations</h1>
        <p className="text-sm text-gray-600">
          Manage and configure your integrations with various services here. Ensure that all
          necessary keys and settings are correctly entered to enable seamless connectivity.
        </p>
      </div>
<<<<<<< Updated upstream
      <IntegrationList />
=======
      <div className="mb-6 border-b border-gray-300 sm:flex">
        <div className="w-1/4">{locationSelectorElement}</div>
      </div>

      <Tabs defaultValue="crm" className="space-y-6">
        <TabsList className="h-12 w-full justify-start space-x-4 bg-transparent p-0">
          <TabsTrigger value="crm" className="data-[state=active]:bg-primary h-10 rounded-md px-6">
            CRM Systems
          </TabsTrigger>
          <TabsTrigger value="pos" className="data-[state=active]:bg-primary h-10 rounded-md px-6">
            POS Systems
          </TabsTrigger>
        </TabsList>

        <TabsContent value="crm" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {crmProcessors.map((processor) => (
              <ProcessorCard
                key={processor.id}
                processor={processor}
                onToggle={(enabled) => handleToggle(processor, enabled)}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="pos" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {posProcessors.map((processor) => (
              <ProcessorCard
                key={processor.id}
                processor={processor}
                onToggle={(enabled) => handleToggle(processor, enabled)}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
>>>>>>> Stashed changes
    </div>
  );
}
