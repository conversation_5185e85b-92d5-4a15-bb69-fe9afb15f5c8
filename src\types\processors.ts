export type ProcessorType = 'PAYMENT' | 'CRM' | 'POS';

export interface Processor {
  id: string;
  name: string;
  type: ProcessorType;
  logo?: string;
  enabled: boolean;
  requiresConfig: boolean;
  configFields: {
    clientId?: boolean;
    clientSecret?: boolean;
    apiKey?: boolean;
  };
}

export interface ProcessorConfig {
  clientId?: string;
  clientSecret?: string;
  apiKey?: string;
}
