'use client';

import * as React from 'react';

import { cn } from '@/lib/utils';
import ReactInputMask from 'react-input-mask';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: keyof Variants;
  mask: string;
}

interface Variants {
  default: string;
}

const inputVariants: Variants = {
  default:
    'bg-gray-50 border border-gray-300 text-gray-900 focus:ring-primary-600 focus:border-primary-600 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500',
};

const FormattedInput = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, variant, ...props }, ref) => {
    return (
      // @ts-ignore
      <ReactInputMask {...props}>
        {/* @ts-ignore */}
        <input
          type={type}
          className={cn(
            'block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 px-3 text-sm text-gray-900 !placeholder-gray-400 outline-none focus:border-primary-600 focus:ring-1 focus:ring-primary-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500',
            className,
            type === 'search' && 'pl-10',
            variant && inputVariants[variant],
          )}
          ref={ref}
          {...props}
        />
      </ReactInputMask>
    );
  },
);
FormattedInput.displayName = 'FormattedInput';

export { FormattedInput };
