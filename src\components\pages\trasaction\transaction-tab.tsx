import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ponent,
  <PERSON><PERSON><PERSON>,
  StatusFilter,
  useDataGridView,
  Variant,
} from '@/components/globals';

import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { useLocationSelector } from '@/components/hooks';
import moment from 'moment';
import { useEffect, useMemo, useState } from 'react';
import { TransactionDetailsModal } from './transaction-details-modal';
import { Gateway_TransactionsDocument } from '@/graphql/generated/graphql';
import { useSearchParams } from 'next/navigation';
import useDebounce from '@/components/hooks/useDebounce';
import { CardBrand } from '@/components/shared/components';
import { moneyFormat } from '@/lib/utils';
import { hashCustomerData } from '@/lib/hashUtils';
import { StaticInfoBox } from '@/components/globals/info-box/StaticInfoBox';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';

export enum TransactionStatus {
  // pending = 'pending',
  // completed = 'completed',
  // failed = 'failed',
  // settled = 'settled',
  // onHold = 'onHold',
  // refunded = 'refunded',
  // chargeback = 'chargeback',
  // voided = 'voided',
  // declined = 'declined',
  // authorized = 'authorized',
  DECLINCED = 'DECLINED',
  CAPTURED = 'CAPTURED',
  BATCHED = 'BATCHED',
  REVERSED = 'REVERSED',
  PAID = 'PAID',
}
export const getTransactionStatusChip = (status: TransactionStatus): [Variant, string] => {
  const statusMap: Record<TransactionStatus, Variant> = {
    // [TransactionStatus.pending]: 'warning',
    // [TransactionStatus.completed]: 'success',
    // [TransactionStatus.failed]: 'danger',
    // [TransactionStatus.settled]: 'success',
    // [TransactionStatus.onHold]: 'info',
    // [TransactionStatus.refunded]: 'neutral',
    // [TransactionStatus.chargeback]: 'danger',
    [TransactionStatus.DECLINCED]: 'danger',
    [TransactionStatus.CAPTURED]: 'info',
    [TransactionStatus.BATCHED]: 'success',
    [TransactionStatus.REVERSED]: 'warning',
    [TransactionStatus.PAID]: 'success',
  };

  const variant = statusMap[status] || 'neutral';
  const label = status?.charAt(0).toUpperCase() + status?.slice(1);

  return [variant, label];
};

export const TransactionTab = () => {
  const queryParams = useSearchParams();
  const transantionId = queryParams?.get('id');
  const [selectedTransactionId, setSelectedTransactionId] = useState(transantionId ?? null);
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const {
    locationFilter,
    locationSelectorElement,
    loading: loadingGroupList,
  } = useLocationSelector({
    onlyActive: true,
  });

  const {
    data: transactionData,
    loading: transactionDataLoading,
    refetch: refectTransactionData,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    setSortField,
    setIsAscendOrder,
    filterValue,
    setFilterValue,
    maxVariables,
  } = useDataGridView({
    query: Gateway_TransactionsDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
    initialFilterField: 'status',
    initialFilterValue: 'All',
  });

  useEffect(() => {
    if (locationFilter?.id) {
      refectTransactionData();
    }
  }, [locationFilter]);

  const transactionRows = useMemo(() => {
    const data = transactionData?.gateway_transactions?.data;
    if (!data) return [];
    return data
      .filter((transaction): transaction is NonNullable<typeof transaction> => transaction !== null)
      .map((transaction) => ({
        transactionID: transaction.transactionID,
        location: transaction.location,
        date: transaction.date,
        customer: transaction.customer,
        method: transaction.method,
        brand: transaction.brand,
        last4: transaction.last4,
        amount: transaction.amount,
        status: transaction.status,
      }));
  }, [transactionData?.gateway_transactions?.data]);

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: Gateway_TransactionsDocument,
      variables: maxVariables,
    });
    return result?.data?.gateway_transactions?.data ?? [];
  };

  const columns: Column[] = [
    {
      key: 'transactionID',
      header: 'Transaction',
      width: '150px',
      onClick: (row) => setSelectedTransactionId(row.transactionID),
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'customer',
      header: 'Customer',
      width: '150px',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
      renderCell: (row) => <>{hashCustomerData.name(row.customer) ?? '--'}</>,
    },
    {
      key: 'date',
      header: 'Date',
      valueGetter: (row) => moment(row?.date).format('MM/DD/YYYY'),
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'method',
      header: 'Method',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
      renderCell: (row) => <>{row.method ?? '--'}</>,
    },
    {
      key: 'brand',
      header: 'Brand',
      width: '150px',
      renderCell: (row) => (row.brand ? <CardBrand brand={row.brand} /> : <p>--</p>),
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'last4',
      header: 'Last 4',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
      renderCell: (row) => <>{row.last4 ?? '--'}</>,
    },
    {
      key: 'amount',
      header: 'Amount',
      renderCell: (row) => <>{moneyFormat(row.amount)}</>,
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'status',
      header: 'Status',
      width: '100px',
      renderCell: (row) => {
        const [status, label] = getTransactionStatusChip(row.status);
        return <StatusChip variant={status} label={label} />;
      },
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
  ];

  return (
    <>
      <StaticInfoBox />
      <div className="items-bottom flex justify-between">
        <PageHeader text="Transactions" />
        <div className="mt-[20px] h-[38px] p-0" />
      </div>
      <div className="flex justify-between border-b border-gray-300">
        <div className="w-1/4">{locationSelectorElement}</div>

        <TopComponent value={searchValue} setValue={setSearchValue}>
          <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="transactions" />
        </TopComponent>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={transactionRows}
          pageSize={pageSize}
          currentPage={currentPage}
          isLoading={transactionDataLoading || loadingGroupList}
          actionComponent={
            <StatusFilter
              value={filterValue}
              setValue={setFilterValue}
              statusList={Object.values(TransactionStatus)}
            />
          }
          mode="server"
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          totalRecords={transactionData?.gateway_transactions?.page?.total ?? 0}
        />
      </div>
      <TransactionDetailsModal
        isOpen={selectedTransactionId !== null}
        onClose={() => setSelectedTransactionId(null)}
        queryData={{
          transactionID: selectedTransactionId ?? '',
          groupID: locationFilter?.id ?? '',
        }}
      />
    </>
  );
};
