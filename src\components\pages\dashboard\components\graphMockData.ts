import moment from 'moment';

function generateTimeEntries(
  timeframe: 'everyHour' | 'weekly' | 'monthly',
  baseValue: number,
  counts: number,
) {
  const now = moment();
  const entries: { time: number; value: string }[] = [];
  let numPoints: number;

  switch (timeframe) {
    case 'everyHour':
      numPoints = Math.min(counts, 24);
      for (let i = 0; i < numPoints; i++) {
        entries.push({
          time: now.clone().subtract(1, 'day').startOf('day').add(i, 'hours').valueOf(),
          value: String(Math.floor(baseValue * (0.5 + Math.random()))),
        });
      }
      break;

    case 'weekly':
      numPoints = Math.min(counts, 7);
      for (let i = 0; i < numPoints; i++) {
        entries.push({
          time: now.clone().subtract(1, 'week').startOf('isoWeek').add(i, 'days').valueOf(),
          value: String(Math.floor(baseValue * (0.5 + Math.random()))),
        });
      }
      break;

    case 'monthly':
      numPoints = Math.min(counts, now.clone().subtract(1, 'month').daysInMonth());
      for (let i = 0; i < numPoints; i++) {
        entries.push({
          time: now.clone().subtract(1, 'month').startOf('month').add(i, 'days').valueOf(),
          value: String(Math.floor(baseValue * (0.5 + Math.random()))),
        });
      }
      break;
  }

  return entries;
}

export function generateMockData(timeframe: 'everyHour' | 'weekly' | 'monthly') {
  const now = moment();

  let startDate, endDate, timeStart, timeEnd;
  if (timeframe === 'everyHour') {
    startDate = now.clone().subtract(1, 'day').startOf('day').format('M/D/YYYY');
    endDate = now.clone().subtract(1, 'day').endOf('day').format('M/D/YYYY');
    timeStart = now.clone().subtract(1, 'day').startOf('day').valueOf();
    timeEnd = now.clone().subtract(1, 'day').endOf('day').valueOf();
  } else if (timeframe === 'weekly') {
    startDate = now.clone().subtract(1, 'week').startOf('isoWeek').format('M/D/YYYY');
    endDate = now.clone().subtract(1, 'week').endOf('isoWeek').format('M/D/YYYY');
    timeStart = now.clone().subtract(1, 'week').startOf('isoWeek').valueOf();
    timeEnd = now.clone().subtract(1, 'week').endOf('isoWeek').valueOf();
  } else if (timeframe === 'monthly') {
    startDate = now.clone().subtract(1, 'month').startOf('month').format('M/D/YYYY');
    endDate = now.clone().subtract(1, 'month').endOf('month').format('M/D/YYYY');
    timeStart = now.clone().subtract(1, 'month').startOf('month').valueOf();
    timeEnd = now.clone().subtract(1, 'month').endOf('month').valueOf();
  }

  const countsMap: { [key: string]: number } = {
    everyHour: 12,
    weekly: 27,
    monthly: 300,
  };

  return {
    data: {
      dashboard_get_summary: {
        dateStart: startDate,
        dateEnd: endDate,
        totalPortfolio: 45000,
        captured: {
          total: 32000,
          percentageChange: '15',
          timeStart,
          timeEnd,
          timeEntries: generateTimeEntries(timeframe, 500, countsMap[timeframe]),
        },
        refunds: {
          total: 3000,
          percentageChange: '5',
          timeStart,
          timeEnd,
          timeEntries: generateTimeEntries(timeframe, 300, countsMap[timeframe]),
        },
        batched: {
          total: 28000,
          percentageChange: '10',
          timeStart,
          timeEnd,
          timeEntries: generateTimeEntries(timeframe, 5000, countsMap[timeframe]),
        },
        deposits: {
          total: 25000,
          percentageChange: '12',
          timeStart,
          timeEnd,
          timeEntries: generateTimeEntries(timeframe, 6000, countsMap[timeframe]),
        },
      },
    },
  };
}
