import React, { useEffect, useMemo, useState } from 'react';
import { Modal, Button, TextInput, Table } from 'flowbite-react';
import { HiSearch, HiOutlineDocumentDownload } from 'react-icons/hi';
import { Gateway_DepositDocument } from '@/graphql/generated/graphql';
import { useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import { getTransactionStatusChip } from '../trasaction/transaction-tab';
import { StatusChip } from '@/components/globals';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import moment from 'moment';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { useRouter } from 'next/navigation';
import { formatToLocalDateString } from '@/components/shared/utils';
import { CardBrand } from '@/components/shared/components';
import { moneyFormat } from '@/lib/utils';

type DepositDetailsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  queryData: {
    depositID: string;
    groupID: string;
  };
};

const DepositDetailsModal: React.FC<DepositDetailsModalProps> = ({
  isOpen,
  onClose,
  queryData,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const { depositID, groupID } = queryData;
  const router = useRouter();

  const { data, loading, error, refetch } = useQuery(Gateway_DepositDocument, {
    variables: {
      input: {
        data: {
          depositID,
        },
        groupID,
      },
    },
    skip: !depositID || !groupID,
  });
  const depositData = data?.gateway_deposit;

  useEffect(() => {
    refetch();
  }, [depositID, groupID]);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const transactionRows = useMemo(() => {
    if (!depositData?.transactions) return [];

    return depositData.transactions
      .filter((transaction): transaction is NonNullable<typeof transaction> => transaction !== null)
      .map((transaction) => ({
        transactionID: transaction.transactionID ?? '',
        date: transaction.date ?? '',
        method: transaction.method ?? '',
        name: transaction.name ?? '',
        last4: transaction.last4 ?? '',
        customer: transaction.customer ?? '',
        amount: transaction.amount ?? 0,
        brand: transaction.brand ?? '',
        status: transaction.status ?? '',
      }));
  }, [depositData?.transactions]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement search logic here
  };
  const columns: Column[] = [
    {
      key: 'transactionID',
      header: 'Transaction #',
      width: '90px',
      sortable: true,
      onClick: (row) => router.push(`/dashboard/reporting?id=${row.transactionID}`),
    },
    {
      key: 'date',
      header: 'Date',
      sortable: true,
      valueGetter: (row) => moment(row?.date).format('MM/DD/YYYY'),
    },
    {
      key: 'method',
      header: 'Method',
      sortable: true,
    },
    {
      key: 'customer',
      header: 'Name',
      width: '150px',
      sortable: true,
    },
    {
      key: 'last4',
      header: 'Last 4',
      sortable: true,
    },
    {
      key: 'amount',
      header: 'Amount',
      sortable: true,
      width: '40px',
    },
    {
      key: 'brand',
      header: 'Brand',
      width: '150px',
      sortable: true,
      renderCell: (row) => <CardBrand brand={row.brand} />,
    },
    {
      key: 'status',
      header: 'Status',
      width: '160px',
      renderCell: (row) => {
        const [status, label] = getTransactionStatusChip(row.status);
        return <StatusChip variant={status} label={label} />;
      },
    },
  ];

  return (
    <Modal show={isOpen} size="6xl" onClose={onClose}>
      <Modal.Header className="flex items-center justify-between border-b">
        <div className="flex items-center space-x-4">
          <Button color="gray" size="sm" onClick={onClose}>
            Back
          </Button>
          <h3 className="text-xl font-semibold">Deposit Details</h3>
        </div>
      </Modal.Header>
      <SpinnerLoading isLoading={loading} />
      {!loading && (
        <Modal.Body>
          <div className="mb-6">
            <div className="mb-4 flex items-center justify-between">
              <div className="space-y-2">
                <p className="">
                  <span className="font-medium">Sweep ID:</span> {depositData?.depositID}
                </p>
                <p className="">
                  <span className="font-medium">Date:</span>{' '}
                  {moment(new Date(depositData?.date ?? '')).format('MMMM DD, YYYY [at] hh:mm A')}
                </p>
                <p className="">
                  <span className="font-medium">Group ID:</span> {groupID}
                </p>
              </div>
              <div className="item-center item-center flex gap-7 text-right">
                {/* <div className="font-medium">Bank Acct: {`*******` + depositData?.last4}</div> */}
                <div className="ml-4 text-4xl font-bold">{moneyFormat(depositData?.amount)}</div>
                {/* <div className="flex gap-3 space-x-2">
                  <Button color="gray" size="sm">
                    Deposit
                  </Button>
                </div> */}
              </div>
            </div>
            <br />
            <p className="">
              Notice that actual payout duration may vary depending on the payment processor. It may
              take up to 1-5 business days for the funds to be deposited into your bank account. If
              you have any concerns, please contact our support team.
            </p>
            <div className="xgrid hidden grid-cols-2 gap-6">
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 font-semibold">Deposit Details</h4>
                <div className="space-y-2">
                  <DetailItem
                    label="Date Deposited"
                    value={formatToLocalDateString(new Date(depositData?.date ?? ''))}
                  />
                  {/* <DetailItem label="Bank Account" value={`*******`+ depositData?.last4} /> */}
                  <DetailItem label="Sales" value={moneyFormat(Number(depositData?.sales ?? 0))} />
                  <DetailItem label="Fees" value={moneyFormat(Number(depositData?.fees ?? 0))} />
                  <DetailItem
                    label="Total Deposit"
                    value={moneyFormat(Number(depositData?.totalDeposit ?? 0))}
                  />
                </div>
              </div>
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 font-semibold">Batches</h4>
                <Table>
                  <Table.Head>
                    <Table.HeadCell>BATCH #</Table.HeadCell>
                    <Table.HeadCell>DEPOSIT ID</Table.HeadCell>
                    <Table.HeadCell>TOTAL PRICE</Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {depositData?.batches?.map((batch) => (
                      <Table.Row key={batch?.batchID}>
                        <Table.Cell>{batch?.batchID}</Table.Cell>
                        <Table.Cell>{batch?.depositID}</Table.Cell>
                        <Table.Cell>{batch?.total}</Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
            </div>
          </div>
          <div className="hidden">
            <h4 className="mb-4 font-semibold">Transactions</h4>
            <div className="mb-4 flex justify-between">
              <form onSubmit={handleSearch} className="flex">
                <TextInput
                  type="text"
                  placeholder="Search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="mr-2 w-full"
                />
                <Button type="submit" color="blue">
                  <HiSearch className="mr-2 h-5 w-5" />
                  Search
                </Button>
              </form>
              <Button color="gray">
                <HiOutlineDocumentDownload className="mr-2 h-5 w-5" />
                Export
              </Button>
            </div>
            <DataGridView
              columns={columns}
              rows={transactionRows}
              isLoading={loading}
              disablePagination
            />
          </div>
        </Modal.Body>
      )}
    </Modal>
  );
};

const DetailItem: React.FC<{ label: string; value: string | number | null | undefined }> = ({
  label,
  value,
}) => (
  <div className="flex justify-between py-2">
    <span className="text-gray-600">{label}:</span>
    <span className="font-medium">{value}</span>
  </div>
);

export default DepositDetailsModal;
