export const mockAccountData = {
  draft: {
    id: 'ACC007',
    dbaName: "Chen's Digital Services",
    legalBusinessName: 'Alex Chen LLC',
    businessEmail: '<EMAIL>',
    businessPhone: '+****************',
    dateAdded: '06/02/2025',
    dateBusinessEstablished: '01/15/2020',
    status: 'draft',
    transactionInfo: {
      avgTransactionAmount: '$500.00',
      monthlyVolume: '$15,000.00',
      businessCategory: 'Digital Services',
      description: 'General retail store selling various consumer goods',
    },
    bankInfo: {
      bankName: 'Sample Bank',
      nameOnAccount: 'Sample Draft Business LLC',
      routingNumber: '********',
      accountNumber: '****4567',
    },
    owners: [
      {
        firstName: 'John',
        lastName: 'Smith',
        title: 'CEO',
        ownershipPercentage: '100',
        email: '<EMAIL>',
      },
    ],
    documentsSubmitted: 2,
    documentsRequired: 5,
  },

  active: {
    id: 'active-456',
    dbaName: 'Active Business Corp',
    businessEmail: '<EMAIL>',
    businessPhone: '(*************',
    dateAdded: '01/10/2025',
    status: 'active',
    performance: {
      monthlyVolume: '$125,000.00',
      lastTransaction: '06/01/2025',
      transactionCount: '1,234',
      averageTicket: '$101.30',
      monthToDateVolume: '$45,000.00',
      yearToDateVolume: '$650,000.00',
    },
    bankInfo: {
      bankName: 'JPMORGAN CHASE BANK, NA',
      accountNumber: '**** **** 4321',
      routingNumber: '*********',
    },
  },

  disabled: {
    id: 'disabled-789',
    dbaName: 'Disabled Business Inc',
    businessEmail: '<EMAIL>',
    businessPhone: '(*************',
    dateAdded: '03/15/2025',
    status: 'disabled',
    disabledDate: '06/01/2025',
    lastActiveDate: '05/31/2025',
    reason: 'High Chargeback Rate',
    lastTransaction: '05/31/2025 14:30:00',
    notes: 'Account disabled pending review of recent chargebacks',
  },

  inactive: {
    id: 'inactive-789',
    dbaName: 'Inactive Enterprise',
    businessEmail: '<EMAIL>',
    businessPhone: '(*************',
    dateAdded: '03/20/2024',
    status: 'inactive',
    deactivationDate: '05/15/2025',
    reason: 'Account inactive due to extended period of no activity',
    lastActiveDate: '04/15/2025',
  },

  processing: {
    id: 'processing-101',
    dbaName: 'Processing Company',
    businessEmail: '<EMAIL>',
    businessPhone: '(*************',
    dateAdded: '06/01/2025',
    status: 'processing',
    applicationProgress: {
      estimatedTime: '2-3 business days',
      currentStep: 'Background Check',
      completedSteps: ['Application Submission', 'Initial Review', 'Document Verification'],
      pendingSteps: ['Background Check', 'Final Approval'],
    },
  },

  submitted: {
    id: 'submitted-112',
    dbaName: 'Submitted Business',
    businessEmail: '<EMAIL>',
    businessPhone: '(*************',
    dateAdded: '05/30/2025',
    status: 'submitted',
    submissionDetails: {
      submissionDate: '06/01/2025',
      reviewStartDate: '06/02/2025',
      estimatedCompletionDate: '06/05/2025',
      documentsSubmitted: ['Business License', 'Bank Statements', 'Tax Returns'],
    },
  },
};
