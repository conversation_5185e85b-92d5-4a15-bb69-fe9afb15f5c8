'use client';

import { <PERSON>Header } from '@/components/globals';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { Button } from 'flowbite-react';
import { useState } from 'react';
import { UserDetailsModal } from './user-details-modal';

type UserRow = {
  id: string;
  partnerName: string;
  title: string;
  lastLogin: string;
  earnings: number;
};

export const UsersTab = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);

  // Sample data
  const users: UserRow[] = [
    {
      id: '1',
      partnerName: '<PERSON>',
      title: 'Senior Partner',
      lastLogin: 'Jun 03, 2023',
      earnings: 32456.78,
    },
    {
      id: '2',
      partnerName: '<PERSON>',
      title: 'Regional Partner',
      lastLogin: 'Jun 02, 2023',
      earnings: 28934.56,
    },
  ];

  // Column definitions
  const columns: Column<UserRow>[] = [
    {
      key: 'partnerName',
      header: 'Name',
      width: '200px',
      sortable: true,
      onClick: (row: UserRow) => handleRowClick(row), // Added onClick here as this is our main clickable column
    },
    {
      key: 'title',
      header: 'Title',
      width: '150px',
    },
    {
      key: 'lastLogin',
      header: 'Last Login',
      width: '150px',
    },
    {
      key: 'earnings',
      header: 'Earnings',
      width: '120px',
      valueGetter: (row) => `$${row.earnings.toLocaleString()}`,
    },
  ];

  const handleRowClick = (row: UserRow) => {
    console.log('Row clicked:', row);
    setSelectedUser(row.id);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    console.log('Closing modal, current state:', { modalOpen, selectedUser });
    setModalOpen(false);
    setSelectedUser(null);
  };

  console.log('Current state:', { modalOpen, selectedUser });

  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-between">
        <PageHeader text="Users" />
        <Button
          onClick={() => {
            console.log('Add new user clicked');
            setSelectedUser('new');
            setModalOpen(true);
          }}
        >
          Add New User
        </Button>
      </div>

      <DataGridView
        columns={columns}
        rows={users}
        pageSize={10}
        mode="client"
        className="cursor-pointer"
        onRowClick={handleRowClick} // Kept as a backup click handler
      />

      {modalOpen && (
        <UserDetailsModal
          isOpen={modalOpen}
          onClose={handleCloseModal}
          userId={selectedUser || undefined}
        />
      )}
    </div>
  );
};
