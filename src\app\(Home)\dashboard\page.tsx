'use client';

import { Card, CardContent } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { HelpCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import FinancialDashboard from '@/components/pages/dashboard/components/financial-dashboard';
import { env } from 'next-runtime-env';
import { ComingSoon } from '@/components/globals/coming-soon';
import { useQuery } from '@apollo/client';
import {
  Dashboard_Get_SummaryDocument,
  Dashboard_Location_SummaryDocument,
} from '@/graphql/generated/graphql';
import { moneyFormat } from '@/lib/utils';
import { useLocationSelector } from '@/components/hooks';
import { useRouter } from 'next/navigation';

export default function Component() {
  const isProd = env('NEXT_PUBLIC_COMING_SOON_DASHBOARD') === 'true';
  const router = useRouter();

  const {
    data: dashboardData,
    loading: dashboardDataLoading,
    error: dashboardDataError,
  } = useQuery(Dashboard_Get_SummaryDocument, {
    variables: {
      input: {},
    },
  });

  const {
    data: locationData,
    loading: locationDataLoading,
    error: locationDataError,
  } = useQuery(Dashboard_Location_SummaryDocument, {
    variables: {
      input: {},
    },
  });

  const { handleSetLocation } = useLocationSelector({});

  const handleClick = (locationID) => {
    if (!locationID) return;
    handleSetLocation(locationID);
    router.push('/dashboard/reporting');
  };

<<<<<<< Updated upstream
  const mockLocationData = {
    dashboard_location_summary: {
      data: [
        {
          locationID: '1',
          locationName: 'Downtown Branch',
          currentYearTotal: 7118022.95,
          lastYearTotal: 5796784.32,
          changePercentage: '22.8',
        },
        {
          locationID: '2',
          locationName: 'West Side Mall',
          currentYearTotal: 5745638.36,
          lastYearTotal: 4510606.18,
          changePercentage: '27.4',
        },
        {
          locationID: '3',
          locationName: 'Airport Terminal',
          currentYearTotal: 4163746.0,
          lastYearTotal: 3680335.45,
          changePercentage: '13.1',
        },
        {
          locationID: '4',
          locationName: 'Business District',
          currentYearTotal: 3845756.18,
          lastYearTotal: 2899061.33,
          changePercentage: '32.7',
        },
        {
          locationID: '5',
          locationName: 'Harbor View',
          currentYearTotal: 2945358.44,
          lastYearTotal: 2215622.89,
          changePercentage: '32.9',
        },
      ],
=======
  const mockData = {
    mockLocationData: {
      dashboard_location_summary: {
        data: [
          {
            locationID: '1',
            locationName: 'Downtown Branch',
            currentYearTotal: 7118022.95,
            lastYearTotal: 5796784.32,
            changePercentage: '22.8',
          },
          {
            locationID: '2',
            locationName: 'West Side Mall',
            currentYearTotal: 5745638.36,
            lastYearTotal: 4510606.18,
            changePercentage: '27.4',
          },
          {
            locationID: '3',
            locationName: 'Airport Terminal',
            currentYearTotal: 4163746.0,
            lastYearTotal: 3680335.45,
            changePercentage: '13.1',
          },
          {
            locationID: '4',
            locationName: 'Business District',
            currentYearTotal: 3845756.18,
            lastYearTotal: 2899061.33,
            changePercentage: '32.7',
          },
          {
            locationID: '5',
            locationName: 'Harbor View',
            currentYearTotal: 2945358.44,
            lastYearTotal: 2215622.89,
            changePercentage: '32.9',
          },
        ],
      },
>>>>>>> Stashed changes
    },
  };

<<<<<<< Updated upstream
  const mockDashboardData = {
    dashboard_get_summary: {
      totalPortfolio: 23818521.93,
      captured: { total: 18250000 },
      refunds: { total: 450000 },
      batched: { total: 17800000 },
      deposits: { total: 17500000 },
=======
    mockDashboardData: {
      dashboard_get_summary: {
        totalPortfolio: 23818521.93,
        captured: {
          total: 18250000,
        },
        refunds: {
          total: 450000,
        },
        batched: {
          total: 17800000,
        },
        deposits: {
          total: 17500000,
        },
        recentTransactions: [
          {
            id: 'tx-001',
            date: new Date(Date.now() - 1800 * 1000).toISOString(), // 30 mins ago
            amount: 2499.99,
            type: 'payment',
            status: 'completed',
            description: 'Invoice #12349',
          },
          {
            id: 'tx-002',
            date: new Date(Date.now() - 3600 * 1000).toISOString(), // 1 hour ago
            amount: 1250.0,
            type: 'payment',
            status: 'completed',
            description: 'Invoice #12348',
          },
          {
            id: 'tx-003',
            date: new Date(Date.now() - 7200 * 1000).toISOString(), // 2 hours ago
            amount: 750.5,
            type: 'payment',
            status: 'completed',
            description: 'Invoice #12347',
          },
          {
            id: 'tx-004',
            date: new Date(Date.now() - 10800 * 1000).toISOString(), // 3 hours ago
            amount: 125.0,
            type: 'refund',
            status: 'completed',
            description: 'Refund for Invoice #12340',
          },
        ],
        paymentMethodStats: {
          creditCard: 65, // percentage
          ach: 30,
          other: 5,
        },
        currentMonthMetrics: {
          processedVolume: 2435000.0,
          averageTicketSize: 180.25,
          successRate: 98.5,
          totalTransactions: 13508,
        },
      },
>>>>>>> Stashed changes
    },
  };

  // Use mock data when real data is not available
  const effectiveDashboardData = dashboardData || mockDashboardData;
  const effectiveLocationData = locationData || mockLocationData;

  return (
    <>
      {isProd && <ComingSoon />}
      {!isProd && (
        <div className="container mx-auto mt-12 w-full space-y-8">
          <FinancialDashboard />
          <Card>
            <CardContent className="p-6">
              <div className="mb-4 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <h2 className="text-2xl font-bold">Company Portfolio:</h2>
                  <span className="text-2xl font-bold">
                    {moneyFormat(effectiveDashboardData?.dashboard_get_summary?.totalPortfolio)}
                  </span>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-5 w-5 cursor-help text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Total portfolio value across all locations</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>LOCATION</TableHead>
                    <TableHead>CURRENT YEAR</TableHead>
                    <TableHead>LAST YEAR</TableHead>
                    <TableHead>CHANGE</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {effectiveLocationData?.dashboard_location_summary?.data.map((item) => (
                    <TableRow key={item?.locationID} className="hover:bg-gray-50">
                      <TableCell>
                        <div
                          onClick={() =>
                            handleClick({ id: item?.locationID, label: item?.locationName })
                          }
                          className="flex items-center space-x-2 hover:cursor-pointer"
                        >
                          <span>{item?.locationName}</span>
                        </div>
                      </TableCell>
                      <TableCell>{moneyFormat(item?.currentYearTotal)}</TableCell>
                      <TableCell>{moneyFormat(item?.lastYearTotal)}</TableCell>
                      <TableCell
                        className={
                          (Number(item?.changePercentage) ?? 0) >= 0
                            ? 'text-green-600'
                            : 'text-red-600'
                        }
                      >
                        {(Number(item?.changePercentage) ?? 0) >= 0 ? '+' : ''}
                        {item?.changePercentage}%
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
}

function StatCard({ title, value, change, isPositive, progress }) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="mb-2 flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <span className={`text-sm font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {isPositive ? '↑' : '↓'} {change}%
          </span>
        </div>
        <div className="mb-2 text-2xl font-bold">{value}</div>
        <div className="h-2.5 w-full rounded-full bg-gray-200">
          <div className="h-2.5 rounded-full bg-blue-600" style={{ width: `${progress}%` }}></div>
        </div>
      </CardContent>
    </Card>
  );
}

// const portfolioData = [
//   {
//     location: 'Store 1',
//     icon: '/placeholder.svg?height=24&width=24',
//     sales: '$459.60',
//     changePercent: 1.21,
//     currentYear: '$7,118,022,957',
//     lastYear: '$72,796,784',
//   },
//   {
//     location: 'Store 2',
//     icon: '/placeholder.svg?height=24&width=24',
//     sales: '$9.35',
//     changePercent: -3.21,
//     currentYear: '$745,638,365',
//     lastYear: '$21,510,606',
//   },
//   {
//     location: 'Store 3',
//     icon: '/placeholder.svg?height=24&width=24',
//     sales: '$3.82',
//     changePercent: 0.38,
//     currentYear: '$163,746,003',
//     lastYear: '$680,335',
//   },
//   {
//     location: 'Store 4',
//     icon: '/placeholder.svg?height=24&width=24',
//     sales: '$6.77',
//     changePercent: 2.31,
//     currentYear: '$45,756,182',
//     lastYear: '$1,899,061',
//   },
//   {
//     location: 'Store 5',
//     icon: '/placeholder.svg?height=24&width=24',
//     sales: '$0.0002345',
//     changePercent: -0.03,
//     currentYear: '$145,358,445',
//     lastYear: '$215,622',
//   },
// ];
