'use client';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { PAYMENT_PROCESSORS, CRM_PROCESSORS, POS_PROCESSORS } from '@/consts/processors';
import { ProcessorCard } from '@/components/pages/manage/processor/processor-card';
import { useState } from 'react';
import { Processor } from '@/types/processors';

export default function ProcessorPage() {
  const [paymentProcessors, setPaymentProcessors] = useState(PAYMENT_PROCESSORS);
  const [crmProcessors, setCrmProcessors] = useState(CRM_PROCESSORS);
  const [posProcessors, setPosProcessors] = useState(POS_PROCESSORS);

  const handleToggle = (processor: Processor, enabled: boolean) => {
    const updateProcessor = (p: Processor) => ({
      ...p,
      enabled: p.id === processor.id ? enabled : p.enabled,
    });

    switch (processor.type) {
      case 'PAYMENT':
        setPaymentProcessors(paymentProcessors.map(updateProcessor));
        break;
      case 'CRM':
        setCrmProcessors(crmProcessors.map(updateProcessor));
        break;
      case 'POS':
        setPosProcessors(posProcessors.map(updateProcessor));
        break;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Processor Configuration</h1>
        <p className="text-muted-foreground">
          Enable and configure your payment processors, CRMs, and POS systems.
        </p>
      </div>{' '}
      <Tabs defaultValue="payment" className="space-y-6">
        <TabsList className="h-12 w-full justify-start space-x-4 bg-transparent p-0">
          <TabsTrigger
            value="payment"
            className="data-[state=active]:bg-primary h-10 rounded-md px-6"
          >
            Payment Processors
          </TabsTrigger>
          <TabsTrigger value="crm" className="data-[state=active]:bg-primary h-10 rounded-md px-6">
            CRM Systems
          </TabsTrigger>
          <TabsTrigger value="pos" className="data-[state=active]:bg-primary h-10 rounded-md px-6">
            POS Systems
          </TabsTrigger>
        </TabsList>
        <TabsContent value="payment" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {paymentProcessors.map((processor) => (
              <ProcessorCard
                key={processor.id}
                processor={processor}
                onToggle={(enabled) => handleToggle(processor, enabled)}
              />
            ))}
          </div>
        </TabsContent>
        <TabsContent value="crm" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {crmProcessors.map((processor) => (
              <ProcessorCard
                key={processor.id}
                processor={processor}
                onToggle={(enabled) => handleToggle(processor, enabled)}
              />
            ))}
          </div>
        </TabsContent>
        <TabsContent value="pos" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {posProcessors.map((processor) => (
              <ProcessorCard
                key={processor.id}
                processor={processor}
                onToggle={(enabled) => handleToggle(processor, enabled)}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
