interface User {
  id: string;
  name: string;
  lastName: string;
  email: string;
  title?: string;
  createdAt: string;
  lastLogin: string;
}

interface GroupMember {
  user: User | null;
  access: number; // 3 = Admin, 2 = Agent
  group: {
    id: string;
    name: string;
  };
  invite?: {
    id: string;
    email: string;
  };
}

export const mockUsers: GroupMember[] = [
  {
    user: {
      id: '1',
      name: '<PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      title: 'Senior Partner',
      createdAt: '2023-01-01T00:00:00Z',
      lastLogin: '2023-06-03T00:00:00Z',
    },
    access: 3, // Admin
    group: {
      id: '1',
      name: 'Admin Group',
    },
  },
  {
    user: {
      id: '2',
      name: '<PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      title: 'Regional Partner',
      createdAt: '2023-01-01T00:00:00Z',
      lastLogin: '2023-06-02T00:00:00Z',
    },
    access: 3,
    group: {
      id: '1',
      name: 'Admin Group',
    },
  },
  {
    user: {
      id: '3',
      name: '<PERSON>',
      lastName: '<PERSON>',
      email: 'micha<PERSON>.<EMAIL>',
      title: 'Associate Partner',
      createdAt: '2023-01-01T00:00:00Z',
      lastLogin: '2023-06-01T00:00:00Z',
    },
    access: 2,
    group: {
      id: '1',
      name: 'Admin Group',
    },
  },
  {
    user: {
      id: '4',
      name: 'Emily',
      lastName: 'Davis',
      email: '<EMAIL>',
      title: 'Partner Manager',
      createdAt: '2023-01-01T00:00:00Z',
      lastLogin: '2023-05-31T00:00:00Z',
    },
    access: 2,
    group: {
      id: '1',
      name: 'Admin Group',
    },
  },
  {
    user: {
      id: '5',
      name: 'David',
      lastName: 'Wilson',
      email: '<EMAIL>',
      title: 'Partner Representative',
      createdAt: '2023-01-01T00:00:00Z',
      lastLogin: '2023-05-30T00:00:00Z',
    },
    access: 2,
    group: {
      id: '1',
      name: 'Admin Group',
    },
  },
  // Additional users from original mock data
  {
    user: {
      id: '6',
      name: 'Mike',
      lastName: 'Johnson',
      email: '<EMAIL>',
      title: 'Agent',
      createdAt: '2024-03-20T00:00:00Z',
      lastLogin: '2025-05-28T14:20:00Z',
    },
    access: 2,
    group: {
      id: '1',
      name: 'Admin Group',
    },
  },
  {
    user: {
      id: '7',
      name: 'John',
      lastName: 'Monotoya',
      email: '<EMAIL>',
      title: 'Agent',
      createdAt: '2024-05-01T00:00:00Z',
      lastLogin: '2025-05-31T10:15:00Z',
    },
    access: 2,
    group: {
      id: '1',
      name: 'Admin Group',
    },
  },
  // Invited users
  {
    user: null,
    access: 2,
    group: {
      id: '1',
      name: 'Admin Group',
    },
    invite: {
      id: 'inv1',
      email: '<EMAIL>',
    },
  },
  {
    user: null,
    access: 2,
    group: {
      id: '1',
      name: 'Admin Group',
    },
    invite: {
      id: 'inv2',
      email: '<EMAIL>',
    },
  },
];
