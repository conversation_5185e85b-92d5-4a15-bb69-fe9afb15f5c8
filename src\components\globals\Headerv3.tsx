'use client';

import { Me } from '@/graphql/declarations/me';
import { Notifications } from '@/graphql/declarations/notifications';
import { AUTHSTORE } from '@/lib/auth-storage';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { cn } from '@/lib/utils';
import { useQuery } from '@apollo/client';
import moment from 'moment';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { GET_GROUPS_LIST } from '@/graphql/declarations/group';
import { QueryMode } from '@/graphql/generated/graphql';
import { Button, Modal } from 'flowbite-react';
import { env } from 'next-runtime-env';
import { HiOutlineExclamationCircle } from 'react-icons/hi';
import { DemoTimer } from './headers/demo-timer';
import UserDropdown from './headers/navbar-dropdown-user';

type NavLink = {
  key: string;
  name: string;
  href: string;
  subLinks: { link: string; title: string; reload?: boolean }[];
};

export const ReportingPages = [
  { link: '/dashboard/reporting', title: 'Transaction' },
  { link: '/dashboard/reporting/batches', title: 'Batches' },
  { link: '/dashboard/reporting/deposits', title: 'Deposits' },
  { link: '/dashboard/reporting/disputes', title: 'Disputes' },
  { link: '/dashboard/reporting/earnings', title: 'Earnings' },
];

// Temporarily disabled customer functionality
// export const CustomerPages = [
//   { link: '/dashboard/customers', title: 'Customer' },
//   // { link: '/dashboard/customers/payment-plan', title: 'Payment Plan' },
//   { link: '/dashboard/customers/schedule', title: 'Subscriptions' },
//   { link: '/dashboard/customers/manual-entry', title: 'Manual Entry' },
// ];

export const CatalogPages = [
  { link: '/dashboard/catalog', title: 'Categories' },
  { link: '/dashboard/catalog/products', title: 'Products' },
  { link: '/dashboard/catalog/cart-discounts', title: 'Discounts' },
  { link: '/dashboard/catalog/paylink', title: 'Pay Links' },
  // { link: '/dashboard/catalog/product-discounts', title: 'Product Discounts' },
];

export const AdminPages = [
  { link: '/dashboard/manage/users', title: 'Users' },
  { link: '/dashboard/manage/payments', title: 'Online Payments' },
  { link: '/dashboard/manage/integrations', title: 'Integrations' },
  { link: '/dashboard/manage/processor', title: 'Processor' },
];

export const AccountsPages = [
  { link: '/dashboard/accounts', title: 'Accounts' },
  { link: '/dashboard/accounts/equipments', title: 'Equipments' },
];

export const AffiliatePages = [
  { link: '/dashboard/affiliate', title: 'User' },
  { link: '/dashboard/affiliate/settings', title: 'Settings' },
];

export const BackofficePages = [
  { link: '/dashboard/backoffice/links', title: 'Links' },
  { link: '/dashboard/backoffice/propay-data', title: 'Propay Data' },
  { link: '/dashboard/backoffice/propay-status', title: 'Propay Status' },
  { link: '/dashboard/backoffice/support-tickets', title: 'Support Tickets' },
  { link: '/dashboard/backoffice/earnings', title: 'Earnings' },
];

const Links: NavLink[] = [
  { key: 'dashboard', name: 'Dashboard', href: '/dashboard', subLinks: [] },
  { key: 'reporting', name: 'Reporting', href: '/dashboard/reporting', subLinks: ReportingPages },
  // Temporarily disabled customer functionality
  // { key: 'customers', name: 'Customers', href: '/dashboard/customers', subLinks: CustomerPages },
  // { key: 'catalog', name: 'Catalog', href: '/dashboard/catalog', subLinks: CatalogPages },
  { key: 'manage', name: 'Manage', href: '/dashboard/manage', subLinks: AdminPages },
  { key: 'support', name: 'Support', href: '/dashboard/support', subLinks: [] },
  { key: 'accounts', name: 'Accounts', href: '/dashboard/accounts', subLinks: AccountsPages },
  //NOTE: hide for summit
  { key: 'affiliate', name: 'Affiliate', href: '/dashboard/affiliate', subLinks: AffiliatePages },
  {
    key: 'backoffice',
    name: 'Backoffice',
    href: '/dashboard/backoffice',
    subLinks: BackofficePages,
  },
];

//Same tabs for user without merchat
const affiliateTabs = ['affiliate', 'accounts', 'support', 'backoffice', 'manage'];

const NotificationList = () => {
  const { data: notificationData } = useQuery(Notifications, {
    variables: {
      orderBy: [],
      skip: 0,
      where: {},
    },
  });

  return (
    <div className="my-4 block w-full max-w-sm list-none divide-y divide-gray-100 overflow-hidden rounded text-base dark:divide-gray-600 dark:bg-gray-700">
      <div className="block bg-gray-50 px-4 py-2 text-center text-base font-medium text-gray-700 dark:bg-gray-700 dark:text-gray-400">
        Notifications
      </div>
      <div>
        {notificationData?.notifications?.map((notification, index) => (
          <a
            key={index}
            href="#"
            className="flex border-b px-4 py-3 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            <div className="w-full">
              <div className="mb-1.5 text-sm font-normal text-gray-500 dark:text-gray-400">
                {notification.content}
              </div>
              <div className="text-xs font-medium text-primary-700 dark:text-primary-400">
                {moment(notification.createdAt).fromNow()}
              </div>
            </div>
          </a>
        ))}
      </div>
      <a
        href="#"
        className="block bg-gray-50 py-2 text-center text-base font-medium text-gray-900 hover:bg-gray-100 dark:bg-gray-700 dark:text-white dark:hover:underline"
      >
        <div className="inline-flex items-center">
          <svg
            aria-hidden="true"
            className="mr-2 h-5 w-5"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
            <path
              fillRule="evenodd"
              d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
              clipRule="evenodd"
            ></path>
          </svg>
          View all
        </div>
      </a>
    </div>
  );
};

export default function Header() {
  const nav = usePathname();
  const [initialSetupModal, setInitialSetupModal] = useState(false);
  const router = useRouter();
  // const [isDemo, setIsDemo] = useState(false); // need to set to true when the check is available

  const IS_DEMO = env('NEXT_PUBLIC_DEMO') === 'true';

  const logout = async () => {
    AUTHSTORE.clear();
    apolloClient.resetStore();
    window.location.href = '/login';
  };

  const { data } = useQuery(Me);

  const { data: accountListData } = useQuery(GET_GROUPS_LIST, {
    variables: {
      where: {
        actualName: {
          contains: '',
          mode: QueryMode.Insensitive,
        },
      },
    },
  });
  // useEffect(() => {
  //   if (window !== undefined) {
  //     const baseUrl = window.location.origin;
  //     // setIsDemo(baseUrl === DEMO_URL);
  //     // setIsDemo(true);
  //   }
  // }, []);

  const isActiveMenu = (link: NavLink) => {
    const { subLinks, href } = link;
    const isNavInSubLinks = subLinks.some((subLink) => subLink.link === nav);
    return nav === href || isNavInSubLinks;
  };

  useEffect(() => {
    if (!accountListData) return;
    setInitialSetupModal(accountListData.groups?.length === 0);
  }, [accountListData]);

  //NOTE: Commented for now since affliate still has not logic
  // const isAffiliateOnly = false;

  const filteredLinkes = useMemo(() => {
    if (!accountListData?.groups) return Links;
    const hasMerchant = accountListData?.groups?.length > 0;
    return hasMerchant ? Links : Links.filter((link) => affiliateTabs.includes(link.key));
  }, [accountListData]);

  const [modalOpen, setModalOpen] = useState(false);

  const [notificationOpen, setNotificationOpen] = useState(false);
  const [userDropDown1, setUserDropDown1] = useState(false);

  const handleNavigateToAccounts = () => {
    router.push('/dashboard/accounts');
    setInitialSetupModal(false);
  };

  return (
    <>
      <header className="sticky left-0 top-0 z-[40] w-full">
        <nav className="border border-gray-200 bg-white px-4 py-2.5 antialiased shadow-lg dark:bg-gray-900 lg:px-6">
          <div className="flex flex-wrap items-center justify-between">
            <div className="flex items-center justify-start">
              <a className="mr-6 flex xl:mr-8">
                <Image
                  src="/logo.webp"
                  width={150}
                  height={100}
                  className="mr-3 h-6 sm:h-9"
                  alt="NGnair Logo"
                />
              </a>
              <div className="hidden w-full items-center justify-between lg:order-1 lg:flex lg:w-auto">
                <ul className="mt-4 flex flex-col space-x-6 font-medium lg:mt-0 lg:flex-row xl:space-x-8">
                  {filteredLinkes
                    .filter((link) => {
                      if (link.key === 'affiliate') {
                        return data?.authenticatedItem?.flag_canAffiliate;
                      }
                      if (link.key === 'backoffice') {
                        return data?.authenticatedItem?.role === 'dev';
                      }
                      return true;
                    })
                    .map((link) => (
                      <li key={link.href}>
                        <Link
                          href={link.href}
                          className={cn(
                            'block rounded',
                            isActiveMenu(link)
                              ? 'text-primary-700 dark:text-primary-500'
                              : 'text-gray-500 dark:text-gray-400',
                          )}
                        >
                          {link.name}
                        </Link>
                      </li>
                    ))}
                </ul>
              </div>
            </div>

            <div className="flex items-center justify-between lg:order-2">
              {IS_DEMO && <DemoTimer userId={data?.authenticatedItem?.id ?? null} />}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    type="button"
                    className="rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-900 focus:ring-4 focus:ring-gray-300 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-600"
                  >
                    <span className="sr-only">View notifications</span>
                    <svg
                      className="h-5 w-5"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="currentColor"
                      viewBox="0 0 14 20"
                    >
                      <path d="M12.133 10.632v-1.8A5.406 5.406 0 0 0 7.979 3.57.946.946 0 0 0 8 3.464V1.1a1 1 0 0 0-2 0v2.364a.946.946 0 0 0 .021.106 5.406 5.406 0 0 0-4.154 5.262v1.8C1.867 13.018 0 13.614 0 14.807 0 15.4 0 16 .538 16h12.924C14 16 14 15.4 14 14.807c0-1.193-1.867-1.789-1.867-4.175ZM3.823 17a3.453 3.453 0 0 0 6.354 0H3.823Z" />
                    </svg>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="z-[40] w-[300px] max-w-[80vw]">
                  <NotificationList />
                </DropdownMenuContent>
              </DropdownMenu>

              <span className="mx-2 hidden h-5 w-px bg-gray-200 dark:bg-gray-600 lg:inline"></span>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    type="button"
                    // id="regionDropdownButton"
                    // data-dropdown-toggle="regionDropdown"
                    className="hidden items-center rounded-lg p-2 pl-4 pr-3 font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900 focus:ring-4 focus:ring-gray-300 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-600 md:inline-flex"
                  >
                    {data?.authenticatedItem?.email?.split('@')?.[0] || 'User'}
                    <svg
                      className="ml-1.5 h-2.5 w-2.5"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 10 6"
                    >
                      <path
                        stroke="currentColor"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="m1 1 4 4 4-4"
                      />
                    </svg>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="z-[40] w-[300px] max-w-[80vw]">
                  <UserDropdown
                    name={data?.authenticatedItem?.displayName!}
                    email={data?.authenticatedItem?.email!}
                    onUserSettingsClick={() => {
                      setModalOpen(true);
                    }}
                    onSignOutClick={logout}
                  />
                </DropdownMenuContent>
              </DropdownMenu>

              <button
                type="button"
                id="toggleMobileMenuButton"
                data-collapse-toggle="toggleMobileMenu"
                onClick={() => setUserDropDown1(!userDropDown1)}
                className="items-center rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-900 focus:ring-4 focus:ring-gray-300 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-600 md:ml-2 lg:hidden"
              >
                <span className="sr-only">Open menu</span>
                <svg
                  className="h-6 w-6"
                  aria-hidden="true"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </nav>
        {userDropDown1 && (
          <nav className="bg-white dark:bg-gray-900">
            <ul
              id="toggleMobileMenu"
              className="mt-0 w-full flex-col text-sm font-medium lg:hidden"
            >
              <li className="block border-b dark:border-gray-800 lg:hidden">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button
                      type="button"
                      // id="regionDropdownButton"
                      // data-dropdown-toggle="regionDropdown"
                      className="hidden items-center rounded-lg p-2 pl-4 pr-3 font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900 focus:ring-4 focus:ring-gray-300 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-600 md:inline-flex"
                    >
                      {data?.authenticatedItem?.email?.split('@')?.[0] || 'User'}
                      <svg
                        className="ml-1.5 h-2.5 w-2.5"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 10 6"
                      >
                        <path
                          stroke="currentColor"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="m1 1 4 4 4-4"
                        />
                      </svg>
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="z-[40] w-[300px] max-w-[80vw]">
                    <UserDropdown
                      name={data?.authenticatedItem?.displayName!}
                      email={data?.authenticatedItem?.email!}
                      onUserSettingsClick={() => {
                        setModalOpen(true);
                      }}
                      onSignOutClick={logout}
                    />
                  </DropdownMenuContent>
                </DropdownMenu>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button
                      type="button"
                      data-dropdown-toggle="regionDropdown2"
                      className="flex w-full items-center justify-between px-4 py-3 text-gray-900 dark:text-white lg:px-0 lg:py-0 lg:hover:underline"
                    >
                      <span>{data?.authenticatedItem?.email || 'User'}</span>
                      <svg
                        className="h-2.5 w-2.5 text-gray-500 dark:text-gray-400"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 6 10"
                      >
                        <path
                          stroke="currentColor"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="m1 9 4-4-4-4"
                        />
                      </svg>
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="z-[40] w-[300px] max-w-[80vw]">
                    <UserDropdown
                      name={data?.authenticatedItem?.displayName!}
                      email={data?.authenticatedItem?.email!}
                      onUserSettingsClick={() => {
                        setModalOpen(true);
                      }}
                      onSignOutClick={logout}
                    />
                  </DropdownMenuContent>
                </DropdownMenu>
              </li>
              {Links.map((link) => (
                <li key={link.href} className="block border-b dark:border-gray-700">
                  <Link
                    href={link.href}
                    className={cn(
                      'block px-4 py-3 text-gray-900 dark:text-white lg:px-0 lg:py-0 lg:hover:underline',
                    )}
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        )}
      </header>
      <Modal show={initialSetupModal} size="md" onClose={() => setInitialSetupModal(false)} popup>
        {/* <Modal.Header /> */}
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamationCircle
              color="blue"
              className="mx-auto mb-4 mt-4 h-14 w-14 text-gray-400 dark:text-gray-200"
            />
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              It looks like you don't have a merchants account yet. click below to get started!
            </h3>
            <div className="flex justify-center">
              <Button color="blue" onClick={handleNavigateToAccounts}>
                Get started
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
}
