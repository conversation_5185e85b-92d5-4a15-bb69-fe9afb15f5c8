export const mockEarningsData = {
  totalEarnings: 126543.89,
  totalWithdrawn: 98234.56,
  totalWithdrawable: 28309.33,
  data: [
    {
      user: {
        id: '1',
        name: '<PERSON>',
        title: 'Senior Partner',
        lastLogin: '1685721600000', // June 2, 2023
        earnings: 32456.78,
      },
      merchant: [{ status: 'active' }, { status: 'active' }, { status: 'inactive' }],
    },
    {
      user: {
        id: '2',
        name: '<PERSON>',
        title: 'Regional Partner',
        lastLogin: '1685635200000', // June 1, 2023
        earnings: 28934.56,
      },
      merchant: [
        { status: 'active' },
        { status: 'active' },
        { status: 'active' },
        { status: 'inactive' },
      ],
    },
    {
      user: {
        id: '3',
        name: '<PERSON>',
        title: 'Associate Partner',
        lastLogin: '1685548800000', // May 31, 2023
        earnings: 19876.45,
      },
      merchant: [{ status: 'active' }, { status: 'inactive' }],
    },
    {
      user: {
        id: '4',
        name: '<PERSON>',
        title: 'Partner Manager',
        lastLogin: '1685462400000', // May 30, 2023
        earnings: 24567.89,
      },
      merchant: [
        { status: 'active' },
        { status: 'active' },
        { status: 'inactive' },
        { status: 'inactive' },
      ],
    },
    {
      user: {
        id: '5',
        name: 'David Wilson',
        title: 'Partner Representative',
        lastLogin: '1685376000000', // May 29, 2023
        earnings: 20708.21,
      },
      merchant: [{ status: 'active' }, { status: 'active' }, { status: 'active' }],
    },
  ],
};
