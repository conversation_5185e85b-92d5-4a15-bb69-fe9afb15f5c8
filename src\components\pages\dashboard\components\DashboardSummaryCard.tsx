import { moneyFormat } from '@/lib/utils';
import { BookCopy, ShoppingCart, PiggyBank, ChartNoAxesCombined, Banknote } from 'lucide-react';
import { ChartTab } from './financial-dashboard';
import Link from 'next/link';

interface DashboardSummaryCardProps {
  totals: {
    Captured: number;
    Refunds: number;
    Batched: number;
    Deposits: number;
    'NGnair Earnings'?: number;
  };
}

export const DashboardSummaryCard = ({ totals: rawTotals }: DashboardSummaryCardProps) => {
  // Provide default values for all totals to prevent undefined
  const totals = {
    Captured: rawTotals.Captured ?? 0,
    Refunds: rawTotals.Refunds ?? 0,
    Batched: rawTotals.Batched ?? 0,
    Deposits: rawTotals.Deposits ?? 0,
    'NGnair Earnings': rawTotals['NGnair Earnings'] ?? 0,
  };

  const metrics = [
    {
      label: ChartTab.Captured,
      value: totals.Captured,
      icon: BookCopy,
      color: 'blue',
    },
    {
      label: ChartTab.PartnerEarnings,
      value: totals['NGnair Earnings'],
      icon: PiggyBank,
      color: 'green',
    },
    {
      label: ChartTab.Refunds,
      value: totals.Refunds,
      icon: ChartNoAxesCombined,
      color: 'red',
    },
    {
      label: ChartTab.Batched,
      value: totals.Batched,
      icon: ShoppingCart,
      color: 'gray',
    },
    {
      label: ChartTab.Deposits,
      value: totals.Deposits,
      icon: PiggyBank,
      color: 'gray',
    },
  ];

  return (
    <div className="mb-6 grid grid-cols-1 gap-4 rounded-lg bg-white p-6 shadow-md sm:grid-cols-2 lg:grid-cols-6">
      {metrics.map(({ label, value, icon: Icon, color }) => (
        <div
          key={label}
          className="flex items-center space-x-4 rounded-lg border border-gray-100 bg-white p-4"
        >
          <div className="flex-1">
            <p className="text-sm text-gray-500">{label}</p>
            <p
              className={`mt-1 text-xl font-bold ${
                label === ChartTab.Refunds ? 'text-red-500' : 'text-gray-900'
              }`}
            >
              {moneyFormat(value)}
            </p>
          </div>
        </div>
      ))}

      <div className="flex items-center justify-center lg:col-span-1">
        <Link
          href={'dashboard/customers/manual-entry'}
          className="flex items-center gap-x-2 rounded-lg bg-blue-600 px-4 py-2.5 text-sm font-medium text-white transition-colors hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300"
        >
          <Banknote className="h-4 w-4" /> New payment
        </Link>
      </div>
    </div>
  );
};
