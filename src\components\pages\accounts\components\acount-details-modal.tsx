import { StatusChip } from '@/components/globals';
import LoadingButton from '@/components/globals/loading-button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn, moneyFormat } from '@/lib/utils';
import { Button, Card, Modal, TextInput, ToggleSwitch } from 'flowbite-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { FaRegFlag } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { getMockAccountDetails } from '@/lib/mock/account-details-data';

const bankAccountSchema = z
  .object({
    bank_code: z.string().min(1, { message: 'Bank code is required' }),
    account_number: z.string().min(1, { message: 'Account number is required' }),
    account_number_reenter: z.string().min(1, { message: 'Account number reenter is required' }),
  })
  .refine((data) => data.account_number === data.account_number_reenter, {
    message: 'Account number and re-enter account number must match',
    path: ['account_number_reenter'],
  });

type BankAccountSchema = z.infer<typeof bankAccountSchema>;

export const AccountDetailsModal = ({ isOpen, onClose, accountData: initialData }) => {
  const [isPaymentEnabled, setIsPaymentEnabled] = useState(true);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load mock account details based on the account ID
  const accountData = getMockAccountDetails(initialData.id);

  // Determine if we should show full details based on account status
  const showFullDetails = !['draft', 'Draft - Incomplete Application'].includes(
    accountData.accountStatus,
  );

  const {
    register,
    handleSubmit,
    formState: { isDirty, errors, dirtyFields },
  } = useForm<BankAccountSchema>({
    values: {
      bank_code: accountData.bankInfo.bankCode ?? '',
      account_number: accountData.bankInfo.accountLast4
        ? `**** **** ${accountData.bankInfo.accountLast4}`
        : '',
      account_number_reenter: accountData.bankInfo.accountLast4
        ? `**** **** ${accountData.bankInfo.accountLast4}`
        : '',
    },
    resolver: zodResolver(bankAccountSchema),
  });

  const handleUpdateBankAccountInfo = async (data: BankAccountSchema) => {
    setIsSubmitting(true);
    try {
      // Show OTP modal instead of immediately updating
      setShowOtpModal(true);
    } catch (error) {
      toast.error('Failed to initiate bank account update');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOTPVerification = async (code: string) => {
    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setShowOtpModal(false);
      toast.success('Bank account info updated successfully');
      onClose();
    } catch (error) {
      toast.error('Failed to verify OTP');
    }
  };

  const handleDisablePayments = async () => {
    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setShowConfirmation(false);
      setIsPaymentEnabled(false);
      toast.success('Payment acceptance disabled successfully');
    } catch (error) {
      toast.error('Failed to disable payments');
    }
  };

  return (
    <>
      <Modal show={isOpen} onClose={onClose} size="5xl">
        <Modal.Header className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h3 className="text text-xl font-semibold text-blue-600">{accountData.friendlyName}</h3>
            <StatusChip
              variant={
                accountData.accountStatus === 'active' || accountData.accountStatus === 'approved'
                  ? 'success'
                  : accountData.accountStatus === 'pending' || accountData.accountStatus === 'sent'
                    ? 'info'
                    : accountData.accountStatus === 'reject' ||
                        accountData.accountStatus === 'inactive'
                      ? 'danger'
                      : accountData.accountStatus === 'submitted'
                        ? 'warning'
                        : 'neutral'
              }
              label={
                accountData.accountStatus.charAt(0).toUpperCase() +
                accountData.accountStatus.slice(1)
              }
              big
            />
          </div>
        </Modal.Header>
        <Modal.Body>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <Card className="mb-4">
                <DetailItem label="Date Added" value={accountData.dateAdded} />
                <DetailItem
                  label="Account Status"
                  value={
                    accountData.paymentType.charAt(0).toUpperCase() +
                    accountData.paymentType.slice(1)
                  }
                />
                <div className="flex flex-col gap-3 rounded border-t py-2">
                  <div className="font-semibold text-gray-700">Summary</div>
                  <DetailItem
                    label="This Month Sales"
                    value={moneyFormat(accountData.metrics.monthlySales)}
                  />
                  <DetailItem
                    label="Closed Transactions"
                    value={accountData.metrics.closedTransactions.toLocaleString()}
                  />
                  <DetailItem
                    label="Available Balance"
                    value={moneyFormat(accountData.metrics.availableBalance)}
                  />
                </div>

                <div className="flex flex-col gap-3 rounded border-t py-2">
                  <div className="font-semibold text-gray-700">Limits</div>
                  <DetailItem
                    label="[Card] Per Transaction"
                    value={moneyFormat(accountData.limits.cardPerTransaction)}
                  />
                  <DetailItem
                    label="[Card] Monthly"
                    value={moneyFormat(accountData.limits.cardMonthly)}
                  />
                  <DetailItem
                    label="[Card] Monthly Limit Percentage"
                    value={`${accountData.limits.cardMonthlyPercentage}%`}
                  />
                  <DetailItem
                    label="[Bank Transfer] Per Transaction"
                    value={moneyFormat(accountData.limits.bankTransferPerTransaction)}
                  />
                  <DetailItem
                    label="[Bank Transfer] Monthly"
                    value={moneyFormat(accountData.limits.bankTransferMonthly)}
                  />
                  <DetailItem
                    label="[Bank Transfer] Monthly Limit Percentage"
                    value={`${accountData.limits.bankTransferMonthlyPercentage}%`}
                  />
                </div>
              </Card>
            </div>

            <div>
              <Card className="mb-4">
                <DetailItem label="Business Name" value={accountData.dbaName} />
                <DetailItem label="Email" value={accountData.email} />
                <DetailItem label="Phone" value={accountData.phone} />
                <DetailItem
                  label="Country"
                  value={
                    <span className="flex items-center">
                      <FaRegFlag className="mr-2 text-red-500" />
                      {accountData.country}
                    </span>
                  }
                />
                <DetailItem label="Billing Address" value={accountData.billingAddress} />
              </Card>

              <Card className="mb-4">
                <div className="mt-2 flex items-center justify-between">
                  <span className="font-medium">Payment Acceptance:</span>
                  <ToggleSwitch
                    checked={isPaymentEnabled}
                    onChange={(checked) => {
                      if (!checked) {
                        setShowConfirmation(true);
                      } else {
                        setIsPaymentEnabled(true);
                      }
                    }}
                    label={isPaymentEnabled ? 'Enable' : 'Disable'}
                  />
                </div>
              </Card>

              <Card>
                <h3 className="mb-4 text-lg font-semibold">Bank Account</h3>
                {showFullDetails ? (
                  <form
                    onSubmit={handleSubmit(handleUpdateBankAccountInfo)}
                    className="grid grid-cols-2 gap-4"
                  >
                    <div className="col-span-2">
                      <Label className="text-gray-700">Routing # *</Label>
                      <Input
                        {...register('bank_code')}
                        type="text"
                        placeholder="Enter your Bank's Routing Number"
                        className={cn(errors.bank_code && 'border-red-500')}
                      />
                      {errors.bank_code && (
                        <p className="mt-1 text-sm text-red-500">{errors.bank_code.message}</p>
                      )}
                    </div>
                    <div>
                      <Label className="text-gray-700">Account Number *</Label>
                      <Input
                        {...register('account_number')}
                        type={dirtyFields.account_number ? 'password' : 'text'}
                        placeholder="Enter your Bank Account Number"
                        className={cn(errors.account_number && 'border-red-500')}
                      />
                      {errors.account_number && (
                        <p className="mt-1 text-sm text-red-500">{errors.account_number.message}</p>
                      )}
                    </div>
                    <div>
                      <Label className="text-gray-700">Re-enter Account Number *</Label>
                      <Input
                        {...register('account_number_reenter')}
                        type={dirtyFields.account_number_reenter ? 'password' : 'text'}
                        placeholder="Re-enter your Bank Account Number"
                        className={cn(errors.account_number_reenter && 'border-red-500')}
                      />
                      {errors.account_number_reenter && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.account_number_reenter.message}
                        </p>
                      )}
                    </div>
                    <div className="col-span-2">
                      <Label className="text-gray-700">Bank Name</Label>
                      <p>{accountData.bankInfo.bankName}</p>
                    </div>
                  </form>
                ) : (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="col-span-2">
                      <DetailItem label="Routing #" value={accountData.bankInfo.bankCode} />
                      <DetailItem
                        label="Account Number"
                        value={
                          accountData.bankInfo.accountLast4
                            ? `**** **** ${accountData.bankInfo.accountLast4}`
                            : accountData.bankInfo.accountLast4
                        }
                      />
                      <DetailItem label="Bank Name" value={accountData.bankInfo.bankName} />
                    </div>
                  </div>
                )}
              </Card>
            </div>
          </div>
        </Modal.Body>
      </Modal>

      <OtpVerificationModal
        isOpen={showOtpModal}
        onClose={() => setShowOtpModal(false)}
        onVerify={handleOTPVerification}
      />

      <ConfirmationModal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        onConfirm={handleDisablePayments}
        locationName={accountData.dbaName}
      />
    </>
  );
};

const OtpVerificationModal = ({ isOpen, onClose, onVerify }) => {
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);

      if (value !== '' && index < 5) {
        const nextInput = document.getElementById(`otp-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    setError('');
    try {
      await onVerify(code.join(''));
    } catch (err) {
      setError('Failed to verify OTP');
    }
    setSubmitting(false);
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="md">
      <Modal.Header>Verify OTP</Modal.Header>
      <Modal.Body>
        <div className="space-y-4">
          <p className="text-center">Please enter the 6-digit code sent to your phone</p>
          <div className="flex justify-center space-x-2">
            {code.map((digit, index) => (
              <Input
                key={index}
                id={`otp-${index}`}
                type="text"
                maxLength={1}
                className="h-12 w-12 text-center text-2xl"
                value={digit}
                onChange={(e) => handleCodeChange(index, e.target.value)}
              />
            ))}
          </div>
          {error && <p className="text-center text-sm text-red-500">{error}</p>}
          <div className="flex justify-center">
            <LoadingButton
              isLoading={submitting}
              onClick={handleSubmit}
              variant="primary"
              disabled={code.join('').length !== 6 || submitting}
            >
              {submitting ? 'Verifying...' : 'Verify'}
            </LoadingButton>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

const ConfirmationModal = ({ isOpen, onClose, onConfirm, locationName }) => {
  const [confirmationText, setConfirmationText] = useState('');

  return (
    <Modal show={isOpen} onClose={onClose} size="md">
      <Modal.Header className="flex items-center justify-between">
        <h3 className="text-xl font-semibold">Are you sure?</h3>
      </Modal.Header>
      <Modal.Body>
        <div className="mb-4 border-l-4 border-orange-500 bg-orange-100 p-4 text-orange-700">
          <p>Unexpected bad things will happen if you don't read this!</p>
        </div>
        <p className="mb-4 text-center">
          All transaction processing will be suspended immediately for all payment plans and
          terminals. Batches will not be effected. Are you sure you want to disable payments for{' '}
          {locationName}?
        </p>
        <p className="mb-2 text-center">Please type in the location name to confirm.</p>
        <TextInput
          type="text"
          value={confirmationText}
          onChange={(e) => setConfirmationText(e.target.value)}
          className="mb-4"
        />
        <Button
          color="failure"
          className="w-full"
          onClick={onConfirm}
          disabled={confirmationText !== locationName}
        >
          I understand, disable payments
        </Button>
      </Modal.Body>
    </Modal>
  );
};

const DetailItem = ({ label, value }) => (
  <div className="flex justify-between border-b py-1 last:border-b-0">
    <span className="font-semibold text-gray-700">{label}</span>
    <span className="text-gray-600">{value}</span>
  </div>
);
