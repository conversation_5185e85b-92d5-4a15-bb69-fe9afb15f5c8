import { LoaderSquares } from '@/components/globals/Loaders/Square';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { cn } from '@/lib/utils';
import { Promisable } from '@/types/types';
import { useQuery } from '@apollo/client';
import { Button, Modal } from 'flowbite-react';
import { useSearchParams } from 'next/navigation';
import { useRef, useState } from 'react';
import { HiX } from 'react-icons/hi';
import AddAccountStepper from './add-account-stepper';
import NextPrevious from './next-previous';
import BankPage from './Pages/BankPage';
import BusinessInfoPage from './Pages/BusinessInfoPage';
import OwnershipPage from './Pages/OwnershipPage';
import ReviewPage from './Pages/ReviewPage';
import TransactionsPage from './Pages/TransactionsPage';
import {
  Processor_draft_documentForward,
  Processor_draft_status,
  Processor_draft_submit,
  Processor_draft_update,
} from '@/graphql/declarations/processor';
import TermsPage from './Pages/TermsPage';
import DocumentsPage from './Pages/DocumentsPage';
import { CheckCircleIcon } from 'lucide-react';
import { CrossCircledIcon } from '@radix-ui/react-icons';

export const AddAccountModal = ({ isOpen, onClose }) => {
  const [page, setPage] = useState(1);
  const submitRef = useRef<() => Promisable<boolean>>(() => true);

  const searchParams = useSearchParams();
  const groupID = searchParams?.get('groupID');

  const { data: groupData, loading: groupLoading } = useQuery(Processor_draft_status, {
    variables: {
      input: {
        groupId: groupID!,
      },
    },
    skip: !groupID,
  });

  let groupDataObject = groupID ? groupData?.processor_draft_status : {};

  const [isLoading, setIsLoading] = useState(false);

  const submitForm = async () => {
    const groupID = searchParams?.get('groupID');
    if (!groupID) {
      return;
    }

    submitProgress(true, 'loading', 'Submitting Document');

    try {
      let res = await apolloClient.mutate({
        mutation: Processor_draft_submit,
        variables: {
          input: {
            groupId: groupID,
          },
        },
      });

      submitProgress(true, 'loading', 'Submitting Supporting Files');

      let filesToSubmit = (
        groupDataObject?.files?.filter((file) => !file?.submittedOn) ?? []
      ).filter((file) => !!file?.id);

      await apolloClient.mutate({
        mutation: Processor_draft_documentForward,
        variables: {
          input: {
            fileIDs: filesToSubmit.map((file) => file?.id ?? ''),
            groupId: groupID,
            submissionID: groupID,
          },
        },
      });

      let o = groupData?.processor_draft_status?.files?.map((file) => ({
        ...file,
        submittedOn: file?.submittedOn ?? new Date().toISOString(),
      }));

      await apolloClient.mutate({
        mutation: Processor_draft_update,
        variables: {
          input: {
            groupId: groupID,
            data: {
              files: o
                ? o.map((file) => {
                    return {
                      name: file.name!,
                      b64: file.b64!,
                      category: file.category ?? '',
                      purpose: file.purpose ?? '',
                      id: file.id!,
                      mimetype: file.mimetype!,
                      submittedOn: file.submittedOn!,
                    };
                  })
                : [],
            },
          },
        },
      });

      await apolloClient.resetStore();

      submitProgress(true, 'success', 'Document Submitted');

      await new Promise((resolve) => setTimeout(resolve, 2000));
    } catch (e) {
      submitProgress(true, 'error', 'Something went wrong submitting your document');
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }
    submitProgress(false, '', '');

    // close modal
    onClose();
  };

  const handleCloseModal = () => {
    setPage(1);
    onClose();
  };

  const [submitProgressOpen, setSubmitProgressOpen] = useState(false);
  const [submitInProgress, setSubmitInProgress] = useState('');
  const [submitProgressMessage, setSubmitProgressMessage] = useState('');

  const submitProgress = (running: boolean, isDone: string, message: string) => {
    setSubmitProgressOpen(running);
    setSubmitInProgress(isDone);
    setSubmitProgressMessage(message);
  };

  return (
    <>
      <Modal show={isOpen} onClose={onClose} size="5xl" className="">
        <div className="h-screen overflow-y-scroll">
          <div className="flex items-center justify-between p-2 pt-4">
            <div className="ml-4 flex items-center space-x-4">
              <h2 className="text-2xl font-bold">Merchant Application</h2>
            </div>
            <Button color="gray" size="sm" onClick={handleCloseModal}>
              <HiX className="h-5 w-5" />
            </Button>
          </div>
          <div className="w-full overflow-y-scroll p-2">
            <AddAccountStepper currentStep={page} />
            <div className="hidden px-5">
              <p className="font-bold">{groupDataObject?.businessInfo?.legalBusinessName}</p>
              <p className={cn(groupID ? '' : 'font-bold')}>
                {groupID ? 'Edit Account Onboarding' : 'Onboard a New Account'}
              </p>
            </div>
            <div className="max-h-[65vh] w-full overflow-y-auto p-2 pt-2 2xl:min-h-fit">
              {groupLoading && <LoaderSquares />}
              {!groupLoading && (
                <>
                  {page === 1 && (
                    <BusinessInfoPage
                      triggerSubmit={submitRef}
                      initialData={groupDataObject?.businessInfo}
                    />
                  )}
                  {page === 2 && (
                    <TransactionsPage
                      triggerSubmit={submitRef}
                      initialData={groupDataObject?.transactionInfo}
                    />
                  )}
                  {page === 3 && (
                    <OwnershipPage
                      triggerSubmit={submitRef}
                      initialData={groupDataObject?.owners}
                    />
                  )}
                  {page === 4 && (
                    <BankPage triggerSubmit={submitRef} initialData={groupDataObject?.bankInfo} />
                  )}
                  {page === 5 && (
                    <DocumentsPage triggerSubmit={submitRef} initialData={groupDataObject?.files} />
                  )}
                  {page === 6 && (
                    <TermsPage
                      triggerSubmit={submitRef}
                      initialData={groupDataObject?.attestations}
                    />
                  )}
                  {page === 7 && (
                    <ReviewPage triggerSubmit={submitRef} initialData={groupDataObject} />
                  )}
                </>
              )}
            </div>
          </div>
        </div>
        <NextPrevious
          showPrevious={page !== 1}
          showNext={page !== 7}
          onPreviousClick={() => setPage(page - 1)}
          onNextClick={async () => {
            setIsLoading(true);
            let res = await submitRef?.current();
            if (res) {
              setPage(page + 1);
            }
            setIsLoading(false);
          }}
          errorMessage={groupDataObject?.status?.message}
          isLoading={isLoading}
          showSubmit={page === 7}
          onSubmitClick={async () => {
            setIsLoading(true);
            await submitForm();
            setIsLoading(false);
          }}
        />
      </Modal>
      <Modal show={submitProgressOpen} size="sm" className="p-5">
        <div className="space-y-4 p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Submitting</h2>
          </div>
          {submitInProgress == 'loading' && (
            <div className="flex items-center justify-center">
              <LoaderSquares />
            </div>
          )}
          {submitInProgress == 'success' && (
            <div className="flex items-center justify-center">
              <CheckCircleIcon className="h-10 w-10 text-green-500" />
            </div>
          )}
          {submitInProgress == 'error' && (
            <div className="flex items-center justify-center">
              <CrossCircledIcon className="h-10 w-10 text-red-500" />
            </div>
          )}
          <div className="flex items-center justify-center">
            <p>{submitProgressMessage}</p>
          </div>
        </div>
      </Modal>
    </>
  );
};
