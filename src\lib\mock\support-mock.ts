export type SupportTicket = {
  id: string;
  title: string;
  description: string;
  category: string;
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  createdAt: string;
  lastMessageCreatedAt: string;
  messages: Array<{
    id: string;
    sender: string;
    message: string;
    timestamp: string;
    files: Array<{
      url: string;
      filename: string;
    }>;
  }>;
  assignedTo?: string;
};

export const supportCategories = [
  { id: 'BILLING', label: 'Billing & Payments' },
  { id: 'ACCOUNT', label: 'Account Management' },
  { id: 'ACCESS', label: 'Access & Permissions' },
  { id: 'GENERAL', label: 'General Inquiry' },
  { id: 'COMPLIANCE', label: 'Compliance & Regulations' },
  { id: 'OTHER', label: 'Other' },
];

export const mockSupportTickets: SupportTicket[] = [
  {
    id: 'TICKET-001',
    title: 'Billing cycle query',
    description: 'Need clarification on the current billing cycle and payment schedule',
    category: 'BILLING',
    status: 'OPEN',
    priority: 'MEDIUM',
    createdAt: '2024-01-15T10:00:00Z',
    lastMessageCreatedAt: '2024-01-15T10:00:00Z',
    messages: [
      {
        id: 'MSG-001',
        sender: 'John Smith',
        message: 'Could you help me understand the billing cycle for my account?',
        timestamp: '2024-01-15T10:00:00Z',
        files: [],
      },
    ],
  },
  {
    id: 'TICKET-002',
    title: 'Access permissions issue',
    description: 'Unable to access certain features after recent role update',
    category: 'ACCESS',
    status: 'IN_PROGRESS',
    priority: 'HIGH',
    createdAt: '2024-01-14T15:30:00Z',
    lastMessageCreatedAt: '2024-01-15T09:30:00Z',
    messages: [
      {
        id: 'MSG-002',
        sender: 'Sarah Wilson',
        message: 'My team is unable to access the reporting dashboard after the recent update.',
        timestamp: '2024-01-14T15:30:00Z',
        files: [],
      },
      {
        id: 'MSG-003',
        sender: 'Support Admin',
        message: 'We are looking into this issue. Could you specify which roles are affected?',
        timestamp: '2024-01-15T09:30:00Z',
        files: [],
      },
    ],
    assignedTo: 'Support Team Lead',
  },
  {
    id: 'TICKET-003',
    title: 'Compliance documentation request',
    description: 'Need updated compliance certificates for audit',
    category: 'COMPLIANCE',
    status: 'RESOLVED',
    priority: 'HIGH',
    createdAt: '2024-01-13T11:20:00Z',
    lastMessageCreatedAt: '2024-01-14T16:45:00Z',
    messages: [
      {
        id: 'MSG-004',
        sender: 'Michael Chen',
        message: 'Requesting latest compliance certificates for upcoming audit.',
        timestamp: '2024-01-13T11:20:00Z',
        files: [],
      },
      {
        id: 'MSG-005',
        sender: 'Support Admin',
        message: 'Documents have been attached to this ticket.',
        timestamp: '2024-01-14T16:45:00Z',
        files: [
          {
            url: '/mock-files/compliance-cert-2024.pdf',
            filename: 'compliance-cert-2024.pdf',
          },
        ],
      },
    ],
  },
];
